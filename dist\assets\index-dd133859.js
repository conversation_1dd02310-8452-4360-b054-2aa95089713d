(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function $c(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var yu={exports:{}},Nl={},xu={exports:{}},M={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mr=Symbol.for("react.element"),Vc=Symbol.for("react.portal"),Bc=Symbol.for("react.fragment"),Wc=Symbol.for("react.strict_mode"),Hc=Symbol.for("react.profiler"),Qc=Symbol.for("react.provider"),Gc=Symbol.for("react.context"),Kc=Symbol.for("react.forward_ref"),Yc=Symbol.for("react.suspense"),Xc=Symbol.for("react.memo"),Zc=Symbol.for("react.lazy"),os=Symbol.iterator;function Jc(e){return e===null||typeof e!="object"?null:(e=os&&e[os]||e["@@iterator"],typeof e=="function"?e:null)}var wu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ku=Object.assign,Su={};function Nn(e,t,n){this.props=e,this.context=t,this.refs=Su,this.updater=n||wu}Nn.prototype.isReactComponent={};Nn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Nn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Cu(){}Cu.prototype=Nn.prototype;function ci(e,t,n){this.props=e,this.context=t,this.refs=Su,this.updater=n||wu}var di=ci.prototype=new Cu;di.constructor=ci;ku(di,Nn.prototype);di.isPureReactComponent=!0;var is=Array.isArray,Nu=Object.prototype.hasOwnProperty,fi={current:null},Eu={key:!0,ref:!0,__self:!0,__source:!0};function ju(e,t,n){var r,l={},o=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Nu.call(t,r)&&!Eu.hasOwnProperty(r)&&(l[r]=t[r]);var s=arguments.length-2;if(s===1)l.children=n;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)l[r]===void 0&&(l[r]=s[r]);return{$$typeof:mr,type:e,key:o,ref:i,props:l,_owner:fi.current}}function qc(e,t){return{$$typeof:mr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function pi(e){return typeof e=="object"&&e!==null&&e.$$typeof===mr}function bc(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ss=/\/+/g;function Vl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?bc(""+e.key):t.toString(36)}function $r(e,t,n,r,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case mr:case Vc:i=!0}}if(i)return i=e,l=l(i),e=r===""?"."+Vl(i,0):r,is(l)?(n="",e!=null&&(n=e.replace(ss,"$&/")+"/"),$r(l,t,n,"",function(c){return c})):l!=null&&(pi(l)&&(l=qc(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(ss,"$&/")+"/")+e)),t.push(l)),1;if(i=0,r=r===""?".":r+":",is(e))for(var s=0;s<e.length;s++){o=e[s];var u=r+Vl(o,s);i+=$r(o,t,n,u,l)}else if(u=Jc(e),typeof u=="function")for(e=u.call(e),s=0;!(o=e.next()).done;)o=o.value,u=r+Vl(o,s++),i+=$r(o,t,n,u,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Sr(e,t,n){if(e==null)return e;var r=[],l=0;return $r(e,r,"","",function(o){return t.call(n,o,l++)}),r}function ed(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var fe={current:null},Vr={transition:null},td={ReactCurrentDispatcher:fe,ReactCurrentBatchConfig:Vr,ReactCurrentOwner:fi};function _u(){throw Error("act(...) is not supported in production builds of React.")}M.Children={map:Sr,forEach:function(e,t,n){Sr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Sr(e,function(){t++}),t},toArray:function(e){return Sr(e,function(t){return t})||[]},only:function(e){if(!pi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};M.Component=Nn;M.Fragment=Bc;M.Profiler=Hc;M.PureComponent=ci;M.StrictMode=Wc;M.Suspense=Yc;M.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=td;M.act=_u;M.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ku({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=fi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)Nu.call(t,u)&&!Eu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&s!==void 0?s[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];r.children=s}return{$$typeof:mr,type:e.type,key:l,ref:o,props:r,_owner:i}};M.createContext=function(e){return e={$$typeof:Gc,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Qc,_context:e},e.Consumer=e};M.createElement=ju;M.createFactory=function(e){var t=ju.bind(null,e);return t.type=e,t};M.createRef=function(){return{current:null}};M.forwardRef=function(e){return{$$typeof:Kc,render:e}};M.isValidElement=pi;M.lazy=function(e){return{$$typeof:Zc,_payload:{_status:-1,_result:e},_init:ed}};M.memo=function(e,t){return{$$typeof:Xc,type:e,compare:t===void 0?null:t}};M.startTransition=function(e){var t=Vr.transition;Vr.transition={};try{e()}finally{Vr.transition=t}};M.unstable_act=_u;M.useCallback=function(e,t){return fe.current.useCallback(e,t)};M.useContext=function(e){return fe.current.useContext(e)};M.useDebugValue=function(){};M.useDeferredValue=function(e){return fe.current.useDeferredValue(e)};M.useEffect=function(e,t){return fe.current.useEffect(e,t)};M.useId=function(){return fe.current.useId()};M.useImperativeHandle=function(e,t,n){return fe.current.useImperativeHandle(e,t,n)};M.useInsertionEffect=function(e,t){return fe.current.useInsertionEffect(e,t)};M.useLayoutEffect=function(e,t){return fe.current.useLayoutEffect(e,t)};M.useMemo=function(e,t){return fe.current.useMemo(e,t)};M.useReducer=function(e,t,n){return fe.current.useReducer(e,t,n)};M.useRef=function(e){return fe.current.useRef(e)};M.useState=function(e){return fe.current.useState(e)};M.useSyncExternalStore=function(e,t,n){return fe.current.useSyncExternalStore(e,t,n)};M.useTransition=function(){return fe.current.useTransition()};M.version="18.3.1";xu.exports=M;var X=xu.exports;const nd=$c(X);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rd=X,ld=Symbol.for("react.element"),od=Symbol.for("react.fragment"),id=Object.prototype.hasOwnProperty,sd=rd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ud={key:!0,ref:!0,__self:!0,__source:!0};function zu(e,t,n){var r,l={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)id.call(t,r)&&!ud.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:ld,type:e,key:o,ref:i,props:l,_owner:sd.current}}Nl.Fragment=od;Nl.jsx=zu;Nl.jsxs=zu;yu.exports=Nl;var p=yu.exports,ho={},Pu={exports:{}},Ce={},Tu={exports:{}},Lu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(C,z){var T=C.length;C.push(z);e:for(;0<T;){var O=T-1>>>1,b=C[O];if(0<l(b,z))C[O]=z,C[T]=b,T=O;else break e}}function n(C){return C.length===0?null:C[0]}function r(C){if(C.length===0)return null;var z=C[0],T=C.pop();if(T!==z){C[0]=T;e:for(var O=0,b=C.length,wr=b>>>1;O<wr;){var zt=2*(O+1)-1,$l=C[zt],Pt=zt+1,kr=C[Pt];if(0>l($l,T))Pt<b&&0>l(kr,$l)?(C[O]=kr,C[Pt]=T,O=Pt):(C[O]=$l,C[zt]=T,O=zt);else if(Pt<b&&0>l(kr,T))C[O]=kr,C[Pt]=T,O=Pt;else break e}}return z}function l(C,z){var T=C.sortIndex-z.sortIndex;return T!==0?T:C.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();e.unstable_now=function(){return i.now()-s}}var u=[],c=[],g=1,h=null,m=3,w=!1,k=!1,x=!1,P=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(C){for(var z=n(c);z!==null;){if(z.callback===null)r(c);else if(z.startTime<=C)r(c),z.sortIndex=z.expirationTime,t(u,z);else break;z=n(c)}}function v(C){if(x=!1,f(C),!k)if(n(u)!==null)k=!0,lt(S);else{var z=n(c);z!==null&&Yt(v,z.startTime-C)}}function S(C,z){k=!1,x&&(x=!1,d(_),_=-1),w=!0;var T=m;try{for(f(z),h=n(u);h!==null&&(!(h.expirationTime>z)||C&&!ae());){var O=h.callback;if(typeof O=="function"){h.callback=null,m=h.priorityLevel;var b=O(h.expirationTime<=z);z=e.unstable_now(),typeof b=="function"?h.callback=b:h===n(u)&&r(u),f(z)}else r(u);h=n(u)}if(h!==null)var wr=!0;else{var zt=n(c);zt!==null&&Yt(v,zt.startTime-z),wr=!1}return wr}finally{h=null,m=T,w=!1}}var E=!1,j=null,_=-1,U=5,R=-1;function ae(){return!(e.unstable_now()-R<U)}function D(){if(j!==null){var C=e.unstable_now();R=C;var z=!0;try{z=j(!0,C)}finally{z?rt():(E=!1,j=null)}}else E=!1}var rt;if(typeof a=="function")rt=function(){a(D)};else if(typeof MessageChannel<"u"){var _t=new MessageChannel,xr=_t.port2;_t.port1.onmessage=D,rt=function(){xr.postMessage(null)}}else rt=function(){P(D,0)};function lt(C){j=C,E||(E=!0,rt())}function Yt(C,z){_=P(function(){C(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){k||w||(k=!0,lt(S))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):U=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(C){switch(m){case 1:case 2:case 3:var z=3;break;default:z=m}var T=m;m=z;try{return C()}finally{m=T}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,z){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var T=m;m=C;try{return z()}finally{m=T}},e.unstable_scheduleCallback=function(C,z,T){var O=e.unstable_now();switch(typeof T=="object"&&T!==null?(T=T.delay,T=typeof T=="number"&&0<T?O+T:O):T=O,C){case 1:var b=-1;break;case 2:b=250;break;case 5:b=**********;break;case 4:b=1e4;break;default:b=5e3}return b=T+b,C={id:g++,callback:z,priorityLevel:C,startTime:T,expirationTime:b,sortIndex:-1},T>O?(C.sortIndex=T,t(c,C),n(u)===null&&C===n(c)&&(x?(d(_),_=-1):x=!0,Yt(v,T-O))):(C.sortIndex=b,t(u,C),k||w||(k=!0,lt(S))),C},e.unstable_shouldYield=ae,e.unstable_wrapCallback=function(C){var z=m;return function(){var T=m;m=z;try{return C.apply(this,arguments)}finally{m=T}}}})(Lu);Tu.exports=Lu;var ad=Tu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cd=X,Se=ad;function y(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ru=new Set,Jn={};function Gt(e,t){vn(e,t),vn(e+"Capture",t)}function vn(e,t){for(Jn[e]=t,e=0;e<t.length;e++)Ru.add(t[e])}var qe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),go=Object.prototype.hasOwnProperty,dd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,us={},as={};function fd(e){return go.call(as,e)?!0:go.call(us,e)?!1:dd.test(e)?as[e]=!0:(us[e]=!0,!1)}function pd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function md(e,t,n,r){if(t===null||typeof t>"u"||pd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function pe(e,t,n,r,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var le={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){le[e]=new pe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];le[t]=new pe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){le[e]=new pe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){le[e]=new pe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){le[e]=new pe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){le[e]=new pe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){le[e]=new pe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){le[e]=new pe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){le[e]=new pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var mi=/[\-:]([a-z])/g;function hi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(mi,hi);le[t]=new pe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(mi,hi);le[t]=new pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(mi,hi);le[t]=new pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){le[e]=new pe(e,1,!1,e.toLowerCase(),null,!1,!1)});le.xlinkHref=new pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){le[e]=new pe(e,1,!1,e.toLowerCase(),null,!0,!0)});function gi(e,t,n,r){var l=le.hasOwnProperty(t)?le[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(md(t,n,l,r)&&(n=null),r||l===null?fd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var nt=cd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Cr=Symbol.for("react.element"),Jt=Symbol.for("react.portal"),qt=Symbol.for("react.fragment"),vi=Symbol.for("react.strict_mode"),vo=Symbol.for("react.profiler"),Mu=Symbol.for("react.provider"),Iu=Symbol.for("react.context"),yi=Symbol.for("react.forward_ref"),yo=Symbol.for("react.suspense"),xo=Symbol.for("react.suspense_list"),xi=Symbol.for("react.memo"),ut=Symbol.for("react.lazy"),Ou=Symbol.for("react.offscreen"),cs=Symbol.iterator;function zn(e){return e===null||typeof e!="object"?null:(e=cs&&e[cs]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,Bl;function An(e){if(Bl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bl=t&&t[1]||""}return`
`+Bl+e}var Wl=!1;function Hl(e,t){if(!e||Wl)return"";Wl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),o=r.stack.split(`
`),i=l.length-1,s=o.length-1;1<=i&&0<=s&&l[i]!==o[s];)s--;for(;1<=i&&0<=s;i--,s--)if(l[i]!==o[s]){if(i!==1||s!==1)do if(i--,s--,0>s||l[i]!==o[s]){var u=`
`+l[i].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=i&&0<=s);break}}}finally{Wl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?An(e):""}function hd(e){switch(e.tag){case 5:return An(e.type);case 16:return An("Lazy");case 13:return An("Suspense");case 19:return An("SuspenseList");case 0:case 2:case 15:return e=Hl(e.type,!1),e;case 11:return e=Hl(e.type.render,!1),e;case 1:return e=Hl(e.type,!0),e;default:return""}}function wo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case qt:return"Fragment";case Jt:return"Portal";case vo:return"Profiler";case vi:return"StrictMode";case yo:return"Suspense";case xo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Iu:return(e.displayName||"Context")+".Consumer";case Mu:return(e._context.displayName||"Context")+".Provider";case yi:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case xi:return t=e.displayName||null,t!==null?t:wo(e.type)||"Memo";case ut:t=e._payload,e=e._init;try{return wo(e(t))}catch{}}return null}function gd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wo(t);case 8:return t===vi?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function St(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Du(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vd(e){var t=Du(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Nr(e){e._valueTracker||(e._valueTracker=vd(e))}function Fu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Du(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function el(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ko(e,t){var n=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ds(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=St(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Au(e,t){t=t.checked,t!=null&&gi(e,"checked",t,!1)}function So(e,t){Au(e,t);var n=St(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Co(e,t.type,n):t.hasOwnProperty("defaultValue")&&Co(e,t.type,St(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function fs(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Co(e,t,n){(t!=="number"||el(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Un=Array.isArray;function cn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+St(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function No(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(y(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ps(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(y(92));if(Un(n)){if(1<n.length)throw Error(y(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:St(n)}}function Uu(e,t){var n=St(t.value),r=St(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ms(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function $u(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Eo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?$u(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Er,Vu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Er=Er||document.createElement("div"),Er.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Er.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function qn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Bn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},yd=["Webkit","ms","Moz","O"];Object.keys(Bn).forEach(function(e){yd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Bn[t]=Bn[e]})});function Bu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Bn.hasOwnProperty(e)&&Bn[e]?(""+t).trim():t+"px"}function Wu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Bu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var xd=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function jo(e,t){if(t){if(xd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(y(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(y(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(y(61))}if(t.style!=null&&typeof t.style!="object")throw Error(y(62))}}function _o(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zo=null;function wi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Po=null,dn=null,fn=null;function hs(e){if(e=vr(e)){if(typeof Po!="function")throw Error(y(280));var t=e.stateNode;t&&(t=Pl(t),Po(e.stateNode,e.type,t))}}function Hu(e){dn?fn?fn.push(e):fn=[e]:dn=e}function Qu(){if(dn){var e=dn,t=fn;if(fn=dn=null,hs(e),t)for(e=0;e<t.length;e++)hs(t[e])}}function Gu(e,t){return e(t)}function Ku(){}var Ql=!1;function Yu(e,t,n){if(Ql)return e(t,n);Ql=!0;try{return Gu(e,t,n)}finally{Ql=!1,(dn!==null||fn!==null)&&(Ku(),Qu())}}function bn(e,t){var n=e.stateNode;if(n===null)return null;var r=Pl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(y(231,t,typeof n));return n}var To=!1;if(qe)try{var Pn={};Object.defineProperty(Pn,"passive",{get:function(){To=!0}}),window.addEventListener("test",Pn,Pn),window.removeEventListener("test",Pn,Pn)}catch{To=!1}function wd(e,t,n,r,l,o,i,s,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(g){this.onError(g)}}var Wn=!1,tl=null,nl=!1,Lo=null,kd={onError:function(e){Wn=!0,tl=e}};function Sd(e,t,n,r,l,o,i,s,u){Wn=!1,tl=null,wd.apply(kd,arguments)}function Cd(e,t,n,r,l,o,i,s,u){if(Sd.apply(this,arguments),Wn){if(Wn){var c=tl;Wn=!1,tl=null}else throw Error(y(198));nl||(nl=!0,Lo=c)}}function Kt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Xu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function gs(e){if(Kt(e)!==e)throw Error(y(188))}function Nd(e){var t=e.alternate;if(!t){if(t=Kt(e),t===null)throw Error(y(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return gs(l),e;if(o===r)return gs(l),t;o=o.sibling}throw Error(y(188))}if(n.return!==r.return)n=l,r=o;else{for(var i=!1,s=l.child;s;){if(s===n){i=!0,n=l,r=o;break}if(s===r){i=!0,r=l,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=l;break}if(s===r){i=!0,r=o,n=l;break}s=s.sibling}if(!i)throw Error(y(189))}}if(n.alternate!==r)throw Error(y(190))}if(n.tag!==3)throw Error(y(188));return n.stateNode.current===n?e:t}function Zu(e){return e=Nd(e),e!==null?Ju(e):null}function Ju(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ju(e);if(t!==null)return t;e=e.sibling}return null}var qu=Se.unstable_scheduleCallback,vs=Se.unstable_cancelCallback,Ed=Se.unstable_shouldYield,jd=Se.unstable_requestPaint,Y=Se.unstable_now,_d=Se.unstable_getCurrentPriorityLevel,ki=Se.unstable_ImmediatePriority,bu=Se.unstable_UserBlockingPriority,rl=Se.unstable_NormalPriority,zd=Se.unstable_LowPriority,ea=Se.unstable_IdlePriority,El=null,He=null;function Pd(e){if(He&&typeof He.onCommitFiberRoot=="function")try{He.onCommitFiberRoot(El,e,void 0,(e.current.flags&128)===128)}catch{}}var Ae=Math.clz32?Math.clz32:Rd,Td=Math.log,Ld=Math.LN2;function Rd(e){return e>>>=0,e===0?32:31-(Td(e)/Ld|0)|0}var jr=64,_r=4194304;function $n(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ll(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var s=i&~l;s!==0?r=$n(s):(o&=i,o!==0&&(r=$n(o)))}else i=n&~l,i!==0?r=$n(i):o!==0&&(r=$n(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ae(t),l=1<<n,r|=e[n],t&=~l;return r}function Md(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Id(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-Ae(o),s=1<<i,u=l[i];u===-1?(!(s&n)||s&r)&&(l[i]=Md(s,t)):u<=t&&(e.expiredLanes|=s),o&=~s}}function Ro(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ta(){var e=jr;return jr<<=1,!(jr&4194240)&&(jr=64),e}function Gl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function hr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ae(t),e[t]=n}function Od(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Ae(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function Si(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ae(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var F=0;function na(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var ra,Ci,la,oa,ia,Mo=!1,zr=[],mt=null,ht=null,gt=null,er=new Map,tr=new Map,ct=[],Dd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ys(e,t){switch(e){case"focusin":case"focusout":mt=null;break;case"dragenter":case"dragleave":ht=null;break;case"mouseover":case"mouseout":gt=null;break;case"pointerover":case"pointerout":er.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":tr.delete(t.pointerId)}}function Tn(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=vr(t),t!==null&&Ci(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Fd(e,t,n,r,l){switch(t){case"focusin":return mt=Tn(mt,e,t,n,r,l),!0;case"dragenter":return ht=Tn(ht,e,t,n,r,l),!0;case"mouseover":return gt=Tn(gt,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return er.set(o,Tn(er.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,tr.set(o,Tn(tr.get(o)||null,e,t,n,r,l)),!0}return!1}function sa(e){var t=Dt(e.target);if(t!==null){var n=Kt(t);if(n!==null){if(t=n.tag,t===13){if(t=Xu(n),t!==null){e.blockedOn=t,ia(e.priority,function(){la(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Br(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Io(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);zo=r,n.target.dispatchEvent(r),zo=null}else return t=vr(n),t!==null&&Ci(t),e.blockedOn=n,!1;t.shift()}return!0}function xs(e,t,n){Br(e)&&n.delete(t)}function Ad(){Mo=!1,mt!==null&&Br(mt)&&(mt=null),ht!==null&&Br(ht)&&(ht=null),gt!==null&&Br(gt)&&(gt=null),er.forEach(xs),tr.forEach(xs)}function Ln(e,t){e.blockedOn===t&&(e.blockedOn=null,Mo||(Mo=!0,Se.unstable_scheduleCallback(Se.unstable_NormalPriority,Ad)))}function nr(e){function t(l){return Ln(l,e)}if(0<zr.length){Ln(zr[0],e);for(var n=1;n<zr.length;n++){var r=zr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(mt!==null&&Ln(mt,e),ht!==null&&Ln(ht,e),gt!==null&&Ln(gt,e),er.forEach(t),tr.forEach(t),n=0;n<ct.length;n++)r=ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ct.length&&(n=ct[0],n.blockedOn===null);)sa(n),n.blockedOn===null&&ct.shift()}var pn=nt.ReactCurrentBatchConfig,ol=!0;function Ud(e,t,n,r){var l=F,o=pn.transition;pn.transition=null;try{F=1,Ni(e,t,n,r)}finally{F=l,pn.transition=o}}function $d(e,t,n,r){var l=F,o=pn.transition;pn.transition=null;try{F=4,Ni(e,t,n,r)}finally{F=l,pn.transition=o}}function Ni(e,t,n,r){if(ol){var l=Io(e,t,n,r);if(l===null)no(e,t,r,il,n),ys(e,r);else if(Fd(l,e,t,n,r))r.stopPropagation();else if(ys(e,r),t&4&&-1<Dd.indexOf(e)){for(;l!==null;){var o=vr(l);if(o!==null&&ra(o),o=Io(e,t,n,r),o===null&&no(e,t,r,il,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else no(e,t,r,null,n)}}var il=null;function Io(e,t,n,r){if(il=null,e=wi(r),e=Dt(e),e!==null)if(t=Kt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Xu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return il=e,null}function ua(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(_d()){case ki:return 1;case bu:return 4;case rl:case zd:return 16;case ea:return 536870912;default:return 16}default:return 16}}var ft=null,Ei=null,Wr=null;function aa(){if(Wr)return Wr;var e,t=Ei,n=t.length,r,l="value"in ft?ft.value:ft.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===l[o-r];r++);return Wr=l.slice(e,1<r?1-r:void 0)}function Hr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Pr(){return!0}function ws(){return!1}function Ne(e){function t(n,r,l,o,i){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(o):o[s]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Pr:ws,this.isPropagationStopped=ws,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Pr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Pr)},persist:function(){},isPersistent:Pr}),t}var En={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ji=Ne(En),gr=G({},En,{view:0,detail:0}),Vd=Ne(gr),Kl,Yl,Rn,jl=G({},gr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_i,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Rn&&(Rn&&e.type==="mousemove"?(Kl=e.screenX-Rn.screenX,Yl=e.screenY-Rn.screenY):Yl=Kl=0,Rn=e),Kl)},movementY:function(e){return"movementY"in e?e.movementY:Yl}}),ks=Ne(jl),Bd=G({},jl,{dataTransfer:0}),Wd=Ne(Bd),Hd=G({},gr,{relatedTarget:0}),Xl=Ne(Hd),Qd=G({},En,{animationName:0,elapsedTime:0,pseudoElement:0}),Gd=Ne(Qd),Kd=G({},En,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Yd=Ne(Kd),Xd=G({},En,{data:0}),Ss=Ne(Xd),Zd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jd={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qd={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qd[e])?!!t[e]:!1}function _i(){return bd}var ef=G({},gr,{key:function(e){if(e.key){var t=Zd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Hr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jd[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_i,charCode:function(e){return e.type==="keypress"?Hr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Hr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),tf=Ne(ef),nf=G({},jl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Cs=Ne(nf),rf=G({},gr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_i}),lf=Ne(rf),of=G({},En,{propertyName:0,elapsedTime:0,pseudoElement:0}),sf=Ne(of),uf=G({},jl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),af=Ne(uf),cf=[9,13,27,32],zi=qe&&"CompositionEvent"in window,Hn=null;qe&&"documentMode"in document&&(Hn=document.documentMode);var df=qe&&"TextEvent"in window&&!Hn,ca=qe&&(!zi||Hn&&8<Hn&&11>=Hn),Ns=String.fromCharCode(32),Es=!1;function da(e,t){switch(e){case"keyup":return cf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function fa(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var bt=!1;function ff(e,t){switch(e){case"compositionend":return fa(t);case"keypress":return t.which!==32?null:(Es=!0,Ns);case"textInput":return e=t.data,e===Ns&&Es?null:e;default:return null}}function pf(e,t){if(bt)return e==="compositionend"||!zi&&da(e,t)?(e=aa(),Wr=Ei=ft=null,bt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ca&&t.locale!=="ko"?null:t.data;default:return null}}var mf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function js(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!mf[e.type]:t==="textarea"}function pa(e,t,n,r){Hu(r),t=sl(t,"onChange"),0<t.length&&(n=new ji("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,rr=null;function hf(e){Na(e,0)}function _l(e){var t=nn(e);if(Fu(t))return e}function gf(e,t){if(e==="change")return t}var ma=!1;if(qe){var Zl;if(qe){var Jl="oninput"in document;if(!Jl){var _s=document.createElement("div");_s.setAttribute("oninput","return;"),Jl=typeof _s.oninput=="function"}Zl=Jl}else Zl=!1;ma=Zl&&(!document.documentMode||9<document.documentMode)}function zs(){Qn&&(Qn.detachEvent("onpropertychange",ha),rr=Qn=null)}function ha(e){if(e.propertyName==="value"&&_l(rr)){var t=[];pa(t,rr,e,wi(e)),Yu(hf,t)}}function vf(e,t,n){e==="focusin"?(zs(),Qn=t,rr=n,Qn.attachEvent("onpropertychange",ha)):e==="focusout"&&zs()}function yf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return _l(rr)}function xf(e,t){if(e==="click")return _l(t)}function wf(e,t){if(e==="input"||e==="change")return _l(t)}function kf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var $e=typeof Object.is=="function"?Object.is:kf;function lr(e,t){if($e(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!go.call(t,l)||!$e(e[l],t[l]))return!1}return!0}function Ps(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ts(e,t){var n=Ps(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ps(n)}}function ga(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ga(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function va(){for(var e=window,t=el();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=el(e.document)}return t}function Pi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Sf(e){var t=va(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ga(n.ownerDocument.documentElement,n)){if(r!==null&&Pi(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=Ts(n,o);var i=Ts(n,r);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Cf=qe&&"documentMode"in document&&11>=document.documentMode,en=null,Oo=null,Gn=null,Do=!1;function Ls(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Do||en==null||en!==el(r)||(r=en,"selectionStart"in r&&Pi(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gn&&lr(Gn,r)||(Gn=r,r=sl(Oo,"onSelect"),0<r.length&&(t=new ji("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=en)))}function Tr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var tn={animationend:Tr("Animation","AnimationEnd"),animationiteration:Tr("Animation","AnimationIteration"),animationstart:Tr("Animation","AnimationStart"),transitionend:Tr("Transition","TransitionEnd")},ql={},ya={};qe&&(ya=document.createElement("div").style,"AnimationEvent"in window||(delete tn.animationend.animation,delete tn.animationiteration.animation,delete tn.animationstart.animation),"TransitionEvent"in window||delete tn.transitionend.transition);function zl(e){if(ql[e])return ql[e];if(!tn[e])return e;var t=tn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ya)return ql[e]=t[n];return e}var xa=zl("animationend"),wa=zl("animationiteration"),ka=zl("animationstart"),Sa=zl("transitionend"),Ca=new Map,Rs="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Nt(e,t){Ca.set(e,t),Gt(t,[e])}for(var bl=0;bl<Rs.length;bl++){var eo=Rs[bl],Nf=eo.toLowerCase(),Ef=eo[0].toUpperCase()+eo.slice(1);Nt(Nf,"on"+Ef)}Nt(xa,"onAnimationEnd");Nt(wa,"onAnimationIteration");Nt(ka,"onAnimationStart");Nt("dblclick","onDoubleClick");Nt("focusin","onFocus");Nt("focusout","onBlur");Nt(Sa,"onTransitionEnd");vn("onMouseEnter",["mouseout","mouseover"]);vn("onMouseLeave",["mouseout","mouseover"]);vn("onPointerEnter",["pointerout","pointerover"]);vn("onPointerLeave",["pointerout","pointerover"]);Gt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Gt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Gt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Gt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Gt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Gt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Vn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Vn));function Ms(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Cd(r,t,void 0,e),e.currentTarget=null}function Na(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],u=s.instance,c=s.currentTarget;if(s=s.listener,u!==o&&l.isPropagationStopped())break e;Ms(l,s,c),o=u}else for(i=0;i<r.length;i++){if(s=r[i],u=s.instance,c=s.currentTarget,s=s.listener,u!==o&&l.isPropagationStopped())break e;Ms(l,s,c),o=u}}}if(nl)throw e=Lo,nl=!1,Lo=null,e}function V(e,t){var n=t[Vo];n===void 0&&(n=t[Vo]=new Set);var r=e+"__bubble";n.has(r)||(Ea(t,e,2,!1),n.add(r))}function to(e,t,n){var r=0;t&&(r|=4),Ea(n,e,r,t)}var Lr="_reactListening"+Math.random().toString(36).slice(2);function or(e){if(!e[Lr]){e[Lr]=!0,Ru.forEach(function(n){n!=="selectionchange"&&(jf.has(n)||to(n,!1,e),to(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Lr]||(t[Lr]=!0,to("selectionchange",!1,t))}}function Ea(e,t,n,r){switch(ua(t)){case 1:var l=Ud;break;case 4:l=$d;break;default:l=Ni}n=l.bind(null,t,n,e),l=void 0,!To||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function no(e,t,n,r,l){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var s=r.stateNode.containerInfo;if(s===l||s.nodeType===8&&s.parentNode===l)break;if(i===4)for(i=r.return;i!==null;){var u=i.tag;if((u===3||u===4)&&(u=i.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;i=i.return}for(;s!==null;){if(i=Dt(s),i===null)return;if(u=i.tag,u===5||u===6){r=o=i;continue e}s=s.parentNode}}r=r.return}Yu(function(){var c=o,g=wi(n),h=[];e:{var m=Ca.get(e);if(m!==void 0){var w=ji,k=e;switch(e){case"keypress":if(Hr(n)===0)break e;case"keydown":case"keyup":w=tf;break;case"focusin":k="focus",w=Xl;break;case"focusout":k="blur",w=Xl;break;case"beforeblur":case"afterblur":w=Xl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=ks;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Wd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=lf;break;case xa:case wa:case ka:w=Gd;break;case Sa:w=sf;break;case"scroll":w=Vd;break;case"wheel":w=af;break;case"copy":case"cut":case"paste":w=Yd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Cs}var x=(t&4)!==0,P=!x&&e==="scroll",d=x?m!==null?m+"Capture":null:m;x=[];for(var a=c,f;a!==null;){f=a;var v=f.stateNode;if(f.tag===5&&v!==null&&(f=v,d!==null&&(v=bn(a,d),v!=null&&x.push(ir(a,v,f)))),P)break;a=a.return}0<x.length&&(m=new w(m,k,null,n,g),h.push({event:m,listeners:x}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",m&&n!==zo&&(k=n.relatedTarget||n.fromElement)&&(Dt(k)||k[be]))break e;if((w||m)&&(m=g.window===g?g:(m=g.ownerDocument)?m.defaultView||m.parentWindow:window,w?(k=n.relatedTarget||n.toElement,w=c,k=k?Dt(k):null,k!==null&&(P=Kt(k),k!==P||k.tag!==5&&k.tag!==6)&&(k=null)):(w=null,k=c),w!==k)){if(x=ks,v="onMouseLeave",d="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(x=Cs,v="onPointerLeave",d="onPointerEnter",a="pointer"),P=w==null?m:nn(w),f=k==null?m:nn(k),m=new x(v,a+"leave",w,n,g),m.target=P,m.relatedTarget=f,v=null,Dt(g)===c&&(x=new x(d,a+"enter",k,n,g),x.target=f,x.relatedTarget=P,v=x),P=v,w&&k)t:{for(x=w,d=k,a=0,f=x;f;f=Xt(f))a++;for(f=0,v=d;v;v=Xt(v))f++;for(;0<a-f;)x=Xt(x),a--;for(;0<f-a;)d=Xt(d),f--;for(;a--;){if(x===d||d!==null&&x===d.alternate)break t;x=Xt(x),d=Xt(d)}x=null}else x=null;w!==null&&Is(h,m,w,x,!1),k!==null&&P!==null&&Is(h,P,k,x,!0)}}e:{if(m=c?nn(c):window,w=m.nodeName&&m.nodeName.toLowerCase(),w==="select"||w==="input"&&m.type==="file")var S=gf;else if(js(m))if(ma)S=wf;else{S=yf;var E=vf}else(w=m.nodeName)&&w.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(S=xf);if(S&&(S=S(e,c))){pa(h,S,n,g);break e}E&&E(e,m,c),e==="focusout"&&(E=m._wrapperState)&&E.controlled&&m.type==="number"&&Co(m,"number",m.value)}switch(E=c?nn(c):window,e){case"focusin":(js(E)||E.contentEditable==="true")&&(en=E,Oo=c,Gn=null);break;case"focusout":Gn=Oo=en=null;break;case"mousedown":Do=!0;break;case"contextmenu":case"mouseup":case"dragend":Do=!1,Ls(h,n,g);break;case"selectionchange":if(Cf)break;case"keydown":case"keyup":Ls(h,n,g)}var j;if(zi)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else bt?da(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(ca&&n.locale!=="ko"&&(bt||_!=="onCompositionStart"?_==="onCompositionEnd"&&bt&&(j=aa()):(ft=g,Ei="value"in ft?ft.value:ft.textContent,bt=!0)),E=sl(c,_),0<E.length&&(_=new Ss(_,e,null,n,g),h.push({event:_,listeners:E}),j?_.data=j:(j=fa(n),j!==null&&(_.data=j)))),(j=df?ff(e,n):pf(e,n))&&(c=sl(c,"onBeforeInput"),0<c.length&&(g=new Ss("onBeforeInput","beforeinput",null,n,g),h.push({event:g,listeners:c}),g.data=j))}Na(h,t)})}function ir(e,t,n){return{instance:e,listener:t,currentTarget:n}}function sl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=bn(e,n),o!=null&&r.unshift(ir(e,o,l)),o=bn(e,t),o!=null&&r.push(ir(e,o,l))),e=e.return}return r}function Xt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Is(e,t,n,r,l){for(var o=t._reactName,i=[];n!==null&&n!==r;){var s=n,u=s.alternate,c=s.stateNode;if(u!==null&&u===r)break;s.tag===5&&c!==null&&(s=c,l?(u=bn(n,o),u!=null&&i.unshift(ir(n,u,s))):l||(u=bn(n,o),u!=null&&i.push(ir(n,u,s)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var _f=/\r\n?/g,zf=/\u0000|\uFFFD/g;function Os(e){return(typeof e=="string"?e:""+e).replace(_f,`
`).replace(zf,"")}function Rr(e,t,n){if(t=Os(t),Os(e)!==t&&n)throw Error(y(425))}function ul(){}var Fo=null,Ao=null;function Uo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var $o=typeof setTimeout=="function"?setTimeout:void 0,Pf=typeof clearTimeout=="function"?clearTimeout:void 0,Ds=typeof Promise=="function"?Promise:void 0,Tf=typeof queueMicrotask=="function"?queueMicrotask:typeof Ds<"u"?function(e){return Ds.resolve(null).then(e).catch(Lf)}:$o;function Lf(e){setTimeout(function(){throw e})}function ro(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),nr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);nr(t)}function vt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Fs(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var jn=Math.random().toString(36).slice(2),We="__reactFiber$"+jn,sr="__reactProps$"+jn,be="__reactContainer$"+jn,Vo="__reactEvents$"+jn,Rf="__reactListeners$"+jn,Mf="__reactHandles$"+jn;function Dt(e){var t=e[We];if(t)return t;for(var n=e.parentNode;n;){if(t=n[be]||n[We]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Fs(e);e!==null;){if(n=e[We])return n;e=Fs(e)}return t}e=n,n=e.parentNode}return null}function vr(e){return e=e[We]||e[be],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function nn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(y(33))}function Pl(e){return e[sr]||null}var Bo=[],rn=-1;function Et(e){return{current:e}}function B(e){0>rn||(e.current=Bo[rn],Bo[rn]=null,rn--)}function A(e,t){rn++,Bo[rn]=e.current,e.current=t}var Ct={},ue=Et(Ct),ge=Et(!1),Vt=Ct;function yn(e,t){var n=e.type.contextTypes;if(!n)return Ct;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function ve(e){return e=e.childContextTypes,e!=null}function al(){B(ge),B(ue)}function As(e,t,n){if(ue.current!==Ct)throw Error(y(168));A(ue,t),A(ge,n)}function ja(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(y(108,gd(e)||"Unknown",l));return G({},n,r)}function cl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ct,Vt=ue.current,A(ue,e),A(ge,ge.current),!0}function Us(e,t,n){var r=e.stateNode;if(!r)throw Error(y(169));n?(e=ja(e,t,Vt),r.__reactInternalMemoizedMergedChildContext=e,B(ge),B(ue),A(ue,e)):B(ge),A(ge,n)}var Ye=null,Tl=!1,lo=!1;function _a(e){Ye===null?Ye=[e]:Ye.push(e)}function If(e){Tl=!0,_a(e)}function jt(){if(!lo&&Ye!==null){lo=!0;var e=0,t=F;try{var n=Ye;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ye=null,Tl=!1}catch(l){throw Ye!==null&&(Ye=Ye.slice(e+1)),qu(ki,jt),l}finally{F=t,lo=!1}}return null}var ln=[],on=0,dl=null,fl=0,je=[],_e=0,Bt=null,Xe=1,Ze="";function Tt(e,t){ln[on++]=fl,ln[on++]=dl,dl=e,fl=t}function za(e,t,n){je[_e++]=Xe,je[_e++]=Ze,je[_e++]=Bt,Bt=e;var r=Xe;e=Ze;var l=32-Ae(r)-1;r&=~(1<<l),n+=1;var o=32-Ae(t)+l;if(30<o){var i=l-l%5;o=(r&(1<<i)-1).toString(32),r>>=i,l-=i,Xe=1<<32-Ae(t)+l|n<<l|r,Ze=o+e}else Xe=1<<o|n<<l|r,Ze=e}function Ti(e){e.return!==null&&(Tt(e,1),za(e,1,0))}function Li(e){for(;e===dl;)dl=ln[--on],ln[on]=null,fl=ln[--on],ln[on]=null;for(;e===Bt;)Bt=je[--_e],je[_e]=null,Ze=je[--_e],je[_e]=null,Xe=je[--_e],je[_e]=null}var ke=null,we=null,W=!1,Fe=null;function Pa(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function $s(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ke=e,we=vt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ke=e,we=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Bt!==null?{id:Xe,overflow:Ze}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ke=e,we=null,!0):!1;default:return!1}}function Wo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ho(e){if(W){var t=we;if(t){var n=t;if(!$s(e,t)){if(Wo(e))throw Error(y(418));t=vt(n.nextSibling);var r=ke;t&&$s(e,t)?Pa(r,n):(e.flags=e.flags&-4097|2,W=!1,ke=e)}}else{if(Wo(e))throw Error(y(418));e.flags=e.flags&-4097|2,W=!1,ke=e}}}function Vs(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ke=e}function Mr(e){if(e!==ke)return!1;if(!W)return Vs(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Uo(e.type,e.memoizedProps)),t&&(t=we)){if(Wo(e))throw Ta(),Error(y(418));for(;t;)Pa(e,t),t=vt(t.nextSibling)}if(Vs(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(y(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){we=vt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}we=null}}else we=ke?vt(e.stateNode.nextSibling):null;return!0}function Ta(){for(var e=we;e;)e=vt(e.nextSibling)}function xn(){we=ke=null,W=!1}function Ri(e){Fe===null?Fe=[e]:Fe.push(e)}var Of=nt.ReactCurrentBatchConfig;function Mn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(y(309));var r=n.stateNode}if(!r)throw Error(y(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var s=l.refs;i===null?delete s[o]:s[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(y(284));if(!n._owner)throw Error(y(290,e))}return e}function Ir(e,t){throw e=Object.prototype.toString.call(t),Error(y(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Bs(e){var t=e._init;return t(e._payload)}function La(e){function t(d,a){if(e){var f=d.deletions;f===null?(d.deletions=[a],d.flags|=16):f.push(a)}}function n(d,a){if(!e)return null;for(;a!==null;)t(d,a),a=a.sibling;return null}function r(d,a){for(d=new Map;a!==null;)a.key!==null?d.set(a.key,a):d.set(a.index,a),a=a.sibling;return d}function l(d,a){return d=kt(d,a),d.index=0,d.sibling=null,d}function o(d,a,f){return d.index=f,e?(f=d.alternate,f!==null?(f=f.index,f<a?(d.flags|=2,a):f):(d.flags|=2,a)):(d.flags|=1048576,a)}function i(d){return e&&d.alternate===null&&(d.flags|=2),d}function s(d,a,f,v){return a===null||a.tag!==6?(a=fo(f,d.mode,v),a.return=d,a):(a=l(a,f),a.return=d,a)}function u(d,a,f,v){var S=f.type;return S===qt?g(d,a,f.props.children,v,f.key):a!==null&&(a.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===ut&&Bs(S)===a.type)?(v=l(a,f.props),v.ref=Mn(d,a,f),v.return=d,v):(v=Jr(f.type,f.key,f.props,null,d.mode,v),v.ref=Mn(d,a,f),v.return=d,v)}function c(d,a,f,v){return a===null||a.tag!==4||a.stateNode.containerInfo!==f.containerInfo||a.stateNode.implementation!==f.implementation?(a=po(f,d.mode,v),a.return=d,a):(a=l(a,f.children||[]),a.return=d,a)}function g(d,a,f,v,S){return a===null||a.tag!==7?(a=$t(f,d.mode,v,S),a.return=d,a):(a=l(a,f),a.return=d,a)}function h(d,a,f){if(typeof a=="string"&&a!==""||typeof a=="number")return a=fo(""+a,d.mode,f),a.return=d,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case Cr:return f=Jr(a.type,a.key,a.props,null,d.mode,f),f.ref=Mn(d,null,a),f.return=d,f;case Jt:return a=po(a,d.mode,f),a.return=d,a;case ut:var v=a._init;return h(d,v(a._payload),f)}if(Un(a)||zn(a))return a=$t(a,d.mode,f,null),a.return=d,a;Ir(d,a)}return null}function m(d,a,f,v){var S=a!==null?a.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return S!==null?null:s(d,a,""+f,v);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Cr:return f.key===S?u(d,a,f,v):null;case Jt:return f.key===S?c(d,a,f,v):null;case ut:return S=f._init,m(d,a,S(f._payload),v)}if(Un(f)||zn(f))return S!==null?null:g(d,a,f,v,null);Ir(d,f)}return null}function w(d,a,f,v,S){if(typeof v=="string"&&v!==""||typeof v=="number")return d=d.get(f)||null,s(a,d,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Cr:return d=d.get(v.key===null?f:v.key)||null,u(a,d,v,S);case Jt:return d=d.get(v.key===null?f:v.key)||null,c(a,d,v,S);case ut:var E=v._init;return w(d,a,f,E(v._payload),S)}if(Un(v)||zn(v))return d=d.get(f)||null,g(a,d,v,S,null);Ir(a,v)}return null}function k(d,a,f,v){for(var S=null,E=null,j=a,_=a=0,U=null;j!==null&&_<f.length;_++){j.index>_?(U=j,j=null):U=j.sibling;var R=m(d,j,f[_],v);if(R===null){j===null&&(j=U);break}e&&j&&R.alternate===null&&t(d,j),a=o(R,a,_),E===null?S=R:E.sibling=R,E=R,j=U}if(_===f.length)return n(d,j),W&&Tt(d,_),S;if(j===null){for(;_<f.length;_++)j=h(d,f[_],v),j!==null&&(a=o(j,a,_),E===null?S=j:E.sibling=j,E=j);return W&&Tt(d,_),S}for(j=r(d,j);_<f.length;_++)U=w(j,d,_,f[_],v),U!==null&&(e&&U.alternate!==null&&j.delete(U.key===null?_:U.key),a=o(U,a,_),E===null?S=U:E.sibling=U,E=U);return e&&j.forEach(function(ae){return t(d,ae)}),W&&Tt(d,_),S}function x(d,a,f,v){var S=zn(f);if(typeof S!="function")throw Error(y(150));if(f=S.call(f),f==null)throw Error(y(151));for(var E=S=null,j=a,_=a=0,U=null,R=f.next();j!==null&&!R.done;_++,R=f.next()){j.index>_?(U=j,j=null):U=j.sibling;var ae=m(d,j,R.value,v);if(ae===null){j===null&&(j=U);break}e&&j&&ae.alternate===null&&t(d,j),a=o(ae,a,_),E===null?S=ae:E.sibling=ae,E=ae,j=U}if(R.done)return n(d,j),W&&Tt(d,_),S;if(j===null){for(;!R.done;_++,R=f.next())R=h(d,R.value,v),R!==null&&(a=o(R,a,_),E===null?S=R:E.sibling=R,E=R);return W&&Tt(d,_),S}for(j=r(d,j);!R.done;_++,R=f.next())R=w(j,d,_,R.value,v),R!==null&&(e&&R.alternate!==null&&j.delete(R.key===null?_:R.key),a=o(R,a,_),E===null?S=R:E.sibling=R,E=R);return e&&j.forEach(function(D){return t(d,D)}),W&&Tt(d,_),S}function P(d,a,f,v){if(typeof f=="object"&&f!==null&&f.type===qt&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Cr:e:{for(var S=f.key,E=a;E!==null;){if(E.key===S){if(S=f.type,S===qt){if(E.tag===7){n(d,E.sibling),a=l(E,f.props.children),a.return=d,d=a;break e}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===ut&&Bs(S)===E.type){n(d,E.sibling),a=l(E,f.props),a.ref=Mn(d,E,f),a.return=d,d=a;break e}n(d,E);break}else t(d,E);E=E.sibling}f.type===qt?(a=$t(f.props.children,d.mode,v,f.key),a.return=d,d=a):(v=Jr(f.type,f.key,f.props,null,d.mode,v),v.ref=Mn(d,a,f),v.return=d,d=v)}return i(d);case Jt:e:{for(E=f.key;a!==null;){if(a.key===E)if(a.tag===4&&a.stateNode.containerInfo===f.containerInfo&&a.stateNode.implementation===f.implementation){n(d,a.sibling),a=l(a,f.children||[]),a.return=d,d=a;break e}else{n(d,a);break}else t(d,a);a=a.sibling}a=po(f,d.mode,v),a.return=d,d=a}return i(d);case ut:return E=f._init,P(d,a,E(f._payload),v)}if(Un(f))return k(d,a,f,v);if(zn(f))return x(d,a,f,v);Ir(d,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,a!==null&&a.tag===6?(n(d,a.sibling),a=l(a,f),a.return=d,d=a):(n(d,a),a=fo(f,d.mode,v),a.return=d,d=a),i(d)):n(d,a)}return P}var wn=La(!0),Ra=La(!1),pl=Et(null),ml=null,sn=null,Mi=null;function Ii(){Mi=sn=ml=null}function Oi(e){var t=pl.current;B(pl),e._currentValue=t}function Qo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function mn(e,t){ml=e,Mi=sn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(he=!0),e.firstContext=null)}function Te(e){var t=e._currentValue;if(Mi!==e)if(e={context:e,memoizedValue:t,next:null},sn===null){if(ml===null)throw Error(y(308));sn=e,ml.dependencies={lanes:0,firstContext:e}}else sn=sn.next=e;return t}var Ft=null;function Di(e){Ft===null?Ft=[e]:Ft.push(e)}function Ma(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Di(t)):(n.next=l.next,l.next=n),t.interleaved=n,et(e,r)}function et(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var at=!1;function Fi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ia(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Je(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function yt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,I&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,et(e,n)}return l=r.interleaved,l===null?(t.next=t,Di(r)):(t.next=l.next,l.next=t),r.interleaved=t,et(e,n)}function Qr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Si(e,n)}}function Ws(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function hl(e,t,n,r){var l=e.updateQueue;at=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,s=l.shared.pending;if(s!==null){l.shared.pending=null;var u=s,c=u.next;u.next=null,i===null?o=c:i.next=c,i=u;var g=e.alternate;g!==null&&(g=g.updateQueue,s=g.lastBaseUpdate,s!==i&&(s===null?g.firstBaseUpdate=c:s.next=c,g.lastBaseUpdate=u))}if(o!==null){var h=l.baseState;i=0,g=c=u=null,s=o;do{var m=s.lane,w=s.eventTime;if((r&m)===m){g!==null&&(g=g.next={eventTime:w,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var k=e,x=s;switch(m=t,w=n,x.tag){case 1:if(k=x.payload,typeof k=="function"){h=k.call(w,h,m);break e}h=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=x.payload,m=typeof k=="function"?k.call(w,h,m):k,m==null)break e;h=G({},h,m);break e;case 2:at=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,m=l.effects,m===null?l.effects=[s]:m.push(s))}else w={eventTime:w,lane:m,tag:s.tag,payload:s.payload,callback:s.callback,next:null},g===null?(c=g=w,u=h):g=g.next=w,i|=m;if(s=s.next,s===null){if(s=l.shared.pending,s===null)break;m=s,s=m.next,m.next=null,l.lastBaseUpdate=m,l.shared.pending=null}}while(1);if(g===null&&(u=h),l.baseState=u,l.firstBaseUpdate=c,l.lastBaseUpdate=g,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);Ht|=i,e.lanes=i,e.memoizedState=h}}function Hs(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(y(191,l));l.call(r)}}}var yr={},Qe=Et(yr),ur=Et(yr),ar=Et(yr);function At(e){if(e===yr)throw Error(y(174));return e}function Ai(e,t){switch(A(ar,t),A(ur,e),A(Qe,yr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Eo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Eo(t,e)}B(Qe),A(Qe,t)}function kn(){B(Qe),B(ur),B(ar)}function Oa(e){At(ar.current);var t=At(Qe.current),n=Eo(t,e.type);t!==n&&(A(ur,e),A(Qe,n))}function Ui(e){ur.current===e&&(B(Qe),B(ur))}var H=Et(0);function gl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var oo=[];function $i(){for(var e=0;e<oo.length;e++)oo[e]._workInProgressVersionPrimary=null;oo.length=0}var Gr=nt.ReactCurrentDispatcher,io=nt.ReactCurrentBatchConfig,Wt=0,Q=null,J=null,ee=null,vl=!1,Kn=!1,cr=0,Df=0;function oe(){throw Error(y(321))}function Vi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!$e(e[n],t[n]))return!1;return!0}function Bi(e,t,n,r,l,o){if(Wt=o,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Gr.current=e===null||e.memoizedState===null?$f:Vf,e=n(r,l),Kn){o=0;do{if(Kn=!1,cr=0,25<=o)throw Error(y(301));o+=1,ee=J=null,t.updateQueue=null,Gr.current=Bf,e=n(r,l)}while(Kn)}if(Gr.current=yl,t=J!==null&&J.next!==null,Wt=0,ee=J=Q=null,vl=!1,t)throw Error(y(300));return e}function Wi(){var e=cr!==0;return cr=0,e}function Be(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ee===null?Q.memoizedState=ee=e:ee=ee.next=e,ee}function Le(){if(J===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=J.next;var t=ee===null?Q.memoizedState:ee.next;if(t!==null)ee=t,J=e;else{if(e===null)throw Error(y(310));J=e,e={memoizedState:J.memoizedState,baseState:J.baseState,baseQueue:J.baseQueue,queue:J.queue,next:null},ee===null?Q.memoizedState=ee=e:ee=ee.next=e}return ee}function dr(e,t){return typeof t=="function"?t(e):t}function so(e){var t=Le(),n=t.queue;if(n===null)throw Error(y(311));n.lastRenderedReducer=e;var r=J,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var s=i=null,u=null,c=o;do{var g=c.lane;if((Wt&g)===g)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:g,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(s=u=h,i=r):u=u.next=h,Q.lanes|=g,Ht|=g}c=c.next}while(c!==null&&c!==o);u===null?i=r:u.next=s,$e(r,t.memoizedState)||(he=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,Q.lanes|=o,Ht|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function uo(e){var t=Le(),n=t.queue;if(n===null)throw Error(y(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);$e(o,t.memoizedState)||(he=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Da(){}function Fa(e,t){var n=Q,r=Le(),l=t(),o=!$e(r.memoizedState,l);if(o&&(r.memoizedState=l,he=!0),r=r.queue,Hi($a.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ee!==null&&ee.memoizedState.tag&1){if(n.flags|=2048,fr(9,Ua.bind(null,n,r,l,t),void 0,null),te===null)throw Error(y(349));Wt&30||Aa(n,t,l)}return l}function Aa(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ua(e,t,n,r){t.value=n,t.getSnapshot=r,Va(t)&&Ba(e)}function $a(e,t,n){return n(function(){Va(t)&&Ba(e)})}function Va(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!$e(e,n)}catch{return!0}}function Ba(e){var t=et(e,1);t!==null&&Ue(t,e,1,-1)}function Qs(e){var t=Be();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:dr,lastRenderedState:e},t.queue=e,e=e.dispatch=Uf.bind(null,Q,e),[t.memoizedState,e]}function fr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Wa(){return Le().memoizedState}function Kr(e,t,n,r){var l=Be();Q.flags|=e,l.memoizedState=fr(1|t,n,void 0,r===void 0?null:r)}function Ll(e,t,n,r){var l=Le();r=r===void 0?null:r;var o=void 0;if(J!==null){var i=J.memoizedState;if(o=i.destroy,r!==null&&Vi(r,i.deps)){l.memoizedState=fr(t,n,o,r);return}}Q.flags|=e,l.memoizedState=fr(1|t,n,o,r)}function Gs(e,t){return Kr(8390656,8,e,t)}function Hi(e,t){return Ll(2048,8,e,t)}function Ha(e,t){return Ll(4,2,e,t)}function Qa(e,t){return Ll(4,4,e,t)}function Ga(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ka(e,t,n){return n=n!=null?n.concat([e]):null,Ll(4,4,Ga.bind(null,t,e),n)}function Qi(){}function Ya(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Xa(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Za(e,t,n){return Wt&21?($e(n,t)||(n=ta(),Q.lanes|=n,Ht|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,he=!0),e.memoizedState=n)}function Ff(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=io.transition;io.transition={};try{e(!1),t()}finally{F=n,io.transition=r}}function Ja(){return Le().memoizedState}function Af(e,t,n){var r=wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},qa(e))ba(t,n);else if(n=Ma(e,t,n,r),n!==null){var l=de();Ue(n,e,r,l),ec(n,t,r)}}function Uf(e,t,n){var r=wt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(qa(e))ba(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,s=o(i,n);if(l.hasEagerState=!0,l.eagerState=s,$e(s,i)){var u=t.interleaved;u===null?(l.next=l,Di(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=Ma(e,t,l,r),n!==null&&(l=de(),Ue(n,e,r,l),ec(n,t,r))}}function qa(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function ba(e,t){Kn=vl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ec(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Si(e,n)}}var yl={readContext:Te,useCallback:oe,useContext:oe,useEffect:oe,useImperativeHandle:oe,useInsertionEffect:oe,useLayoutEffect:oe,useMemo:oe,useReducer:oe,useRef:oe,useState:oe,useDebugValue:oe,useDeferredValue:oe,useTransition:oe,useMutableSource:oe,useSyncExternalStore:oe,useId:oe,unstable_isNewReconciler:!1},$f={readContext:Te,useCallback:function(e,t){return Be().memoizedState=[e,t===void 0?null:t],e},useContext:Te,useEffect:Gs,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Kr(4194308,4,Ga.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Kr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Kr(4,2,e,t)},useMemo:function(e,t){var n=Be();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Be();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Af.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=Be();return e={current:e},t.memoizedState=e},useState:Qs,useDebugValue:Qi,useDeferredValue:function(e){return Be().memoizedState=e},useTransition:function(){var e=Qs(!1),t=e[0];return e=Ff.bind(null,e[1]),Be().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,l=Be();if(W){if(n===void 0)throw Error(y(407));n=n()}else{if(n=t(),te===null)throw Error(y(349));Wt&30||Aa(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Gs($a.bind(null,r,o,e),[e]),r.flags|=2048,fr(9,Ua.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Be(),t=te.identifierPrefix;if(W){var n=Ze,r=Xe;n=(r&~(1<<32-Ae(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=cr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Df++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Vf={readContext:Te,useCallback:Ya,useContext:Te,useEffect:Hi,useImperativeHandle:Ka,useInsertionEffect:Ha,useLayoutEffect:Qa,useMemo:Xa,useReducer:so,useRef:Wa,useState:function(){return so(dr)},useDebugValue:Qi,useDeferredValue:function(e){var t=Le();return Za(t,J.memoizedState,e)},useTransition:function(){var e=so(dr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:Da,useSyncExternalStore:Fa,useId:Ja,unstable_isNewReconciler:!1},Bf={readContext:Te,useCallback:Ya,useContext:Te,useEffect:Hi,useImperativeHandle:Ka,useInsertionEffect:Ha,useLayoutEffect:Qa,useMemo:Xa,useReducer:uo,useRef:Wa,useState:function(){return uo(dr)},useDebugValue:Qi,useDeferredValue:function(e){var t=Le();return J===null?t.memoizedState=e:Za(t,J.memoizedState,e)},useTransition:function(){var e=uo(dr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:Da,useSyncExternalStore:Fa,useId:Ja,unstable_isNewReconciler:!1};function Oe(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Go(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:G({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Rl={isMounted:function(e){return(e=e._reactInternals)?Kt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=de(),l=wt(e),o=Je(r,l);o.payload=t,n!=null&&(o.callback=n),t=yt(e,o,l),t!==null&&(Ue(t,e,l,r),Qr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=de(),l=wt(e),o=Je(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=yt(e,o,l),t!==null&&(Ue(t,e,l,r),Qr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=de(),r=wt(e),l=Je(n,r);l.tag=2,t!=null&&(l.callback=t),t=yt(e,l,r),t!==null&&(Ue(t,e,r,n),Qr(t,e,r))}};function Ks(e,t,n,r,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!lr(n,r)||!lr(l,o):!0}function tc(e,t,n){var r=!1,l=Ct,o=t.contextType;return typeof o=="object"&&o!==null?o=Te(o):(l=ve(t)?Vt:ue.current,r=t.contextTypes,o=(r=r!=null)?yn(e,l):Ct),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Rl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function Ys(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Rl.enqueueReplaceState(t,t.state,null)}function Ko(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Fi(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=Te(o):(o=ve(t)?Vt:ue.current,l.context=yn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Go(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Rl.enqueueReplaceState(l,l.state,null),hl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Sn(e,t){try{var n="",r=t;do n+=hd(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function ao(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Yo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Wf=typeof WeakMap=="function"?WeakMap:Map;function nc(e,t,n){n=Je(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){wl||(wl=!0,li=r),Yo(e,t)},n}function rc(e,t,n){n=Je(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Yo(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Yo(e,t),typeof r!="function"&&(xt===null?xt=new Set([this]):xt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function Xs(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Wf;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=rp.bind(null,e,t,n),t.then(e,e))}function Zs(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Js(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Je(-1,1),t.tag=2,yt(n,t,1))),n.lanes|=1),e)}var Hf=nt.ReactCurrentOwner,he=!1;function ce(e,t,n,r){t.child=e===null?Ra(t,null,n,r):wn(t,e.child,n,r)}function qs(e,t,n,r,l){n=n.render;var o=t.ref;return mn(t,l),r=Bi(e,t,n,r,o,l),n=Wi(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,tt(e,t,l)):(W&&n&&Ti(t),t.flags|=1,ce(e,t,r,l),t.child)}function bs(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!bi(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,lc(e,t,o,r,l)):(e=Jr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:lr,n(i,r)&&e.ref===t.ref)return tt(e,t,l)}return t.flags|=1,e=kt(o,r),e.ref=t.ref,e.return=t,t.child=e}function lc(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref)if(he=!1,t.pendingProps=r=o,(e.lanes&l)!==0)e.flags&131072&&(he=!0);else return t.lanes=e.lanes,tt(e,t,l)}return Xo(e,t,n,r,l)}function oc(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},A(an,xe),xe|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,A(an,xe),xe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,A(an,xe),xe|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,A(an,xe),xe|=r;return ce(e,t,l,n),t.child}function ic(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Xo(e,t,n,r,l){var o=ve(n)?Vt:ue.current;return o=yn(t,o),mn(t,l),n=Bi(e,t,n,r,o,l),r=Wi(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,tt(e,t,l)):(W&&r&&Ti(t),t.flags|=1,ce(e,t,n,l),t.child)}function eu(e,t,n,r,l){if(ve(n)){var o=!0;cl(t)}else o=!1;if(mn(t,l),t.stateNode===null)Yr(e,t),tc(t,n,r),Ko(t,n,r,l),r=!0;else if(e===null){var i=t.stateNode,s=t.memoizedProps;i.props=s;var u=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=Te(c):(c=ve(n)?Vt:ue.current,c=yn(t,c));var g=n.getDerivedStateFromProps,h=typeof g=="function"||typeof i.getSnapshotBeforeUpdate=="function";h||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==r||u!==c)&&Ys(t,i,r,c),at=!1;var m=t.memoizedState;i.state=m,hl(t,r,i,l),u=t.memoizedState,s!==r||m!==u||ge.current||at?(typeof g=="function"&&(Go(t,n,g,r),u=t.memoizedState),(s=at||Ks(t,n,s,r,m,u,c))?(h||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),i.props=r,i.state=u,i.context=c,r=s):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Ia(e,t),s=t.memoizedProps,c=t.type===t.elementType?s:Oe(t.type,s),i.props=c,h=t.pendingProps,m=i.context,u=n.contextType,typeof u=="object"&&u!==null?u=Te(u):(u=ve(n)?Vt:ue.current,u=yn(t,u));var w=n.getDerivedStateFromProps;(g=typeof w=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==h||m!==u)&&Ys(t,i,r,u),at=!1,m=t.memoizedState,i.state=m,hl(t,r,i,l);var k=t.memoizedState;s!==h||m!==k||ge.current||at?(typeof w=="function"&&(Go(t,n,w,r),k=t.memoizedState),(c=at||Ks(t,n,c,r,m,k,u)||!1)?(g||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,k,u),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,k,u)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=k),i.props=r,i.state=k,i.context=u,r=c):(typeof i.componentDidUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return Zo(e,t,n,r,o,l)}function Zo(e,t,n,r,l,o){ic(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return l&&Us(t,n,!1),tt(e,t,o);r=t.stateNode,Hf.current=t;var s=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=wn(t,e.child,null,o),t.child=wn(t,null,s,o)):ce(e,t,s,o),t.memoizedState=r.state,l&&Us(t,n,!0),t.child}function sc(e){var t=e.stateNode;t.pendingContext?As(e,t.pendingContext,t.pendingContext!==t.context):t.context&&As(e,t.context,!1),Ai(e,t.containerInfo)}function tu(e,t,n,r,l){return xn(),Ri(l),t.flags|=256,ce(e,t,n,r),t.child}var Jo={dehydrated:null,treeContext:null,retryLane:0};function qo(e){return{baseLanes:e,cachePool:null,transitions:null}}function uc(e,t,n){var r=t.pendingProps,l=H.current,o=!1,i=(t.flags&128)!==0,s;if((s=i)||(s=e!==null&&e.memoizedState===null?!1:(l&2)!==0),s?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),A(H,l&1),e===null)return Ho(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Ol(i,r,0,null),e=$t(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=qo(n),t.memoizedState=Jo,e):Gi(t,i));if(l=e.memoizedState,l!==null&&(s=l.dehydrated,s!==null))return Qf(e,t,i,r,s,l,n);if(o){o=r.fallback,i=t.mode,l=e.child,s=l.sibling;var u={mode:"hidden",children:r.children};return!(i&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=kt(l,u),r.subtreeFlags=l.subtreeFlags&14680064),s!==null?o=kt(s,o):(o=$t(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?qo(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=Jo,r}return o=e.child,e=o.sibling,r=kt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Gi(e,t){return t=Ol({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Or(e,t,n,r){return r!==null&&Ri(r),wn(t,e.child,null,n),e=Gi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Qf(e,t,n,r,l,o,i){if(n)return t.flags&256?(t.flags&=-257,r=ao(Error(y(422))),Or(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=Ol({mode:"visible",children:r.children},l,0,null),o=$t(o,l,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&wn(t,e.child,null,i),t.child.memoizedState=qo(i),t.memoizedState=Jo,o);if(!(t.mode&1))return Or(e,t,i,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var s=r.dgst;return r=s,o=Error(y(419)),r=ao(o,r,void 0),Or(e,t,i,r)}if(s=(i&e.childLanes)!==0,he||s){if(r=te,r!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,et(e,l),Ue(r,e,l,-1))}return qi(),r=ao(Error(y(421))),Or(e,t,i,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=lp.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,we=vt(l.nextSibling),ke=t,W=!0,Fe=null,e!==null&&(je[_e++]=Xe,je[_e++]=Ze,je[_e++]=Bt,Xe=e.id,Ze=e.overflow,Bt=t),t=Gi(t,r.children),t.flags|=4096,t)}function nu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Qo(e.return,t,n)}function co(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function ac(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(ce(e,t,r.children,n),r=H.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&nu(e,n,t);else if(e.tag===19)nu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(A(H,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&gl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),co(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&gl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}co(t,!0,n,null,o);break;case"together":co(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function tt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ht|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(y(153));if(t.child!==null){for(e=t.child,n=kt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=kt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Gf(e,t,n){switch(t.tag){case 3:sc(t),xn();break;case 5:Oa(t);break;case 1:ve(t.type)&&cl(t);break;case 4:Ai(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;A(pl,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(A(H,H.current&1),t.flags|=128,null):n&t.child.childLanes?uc(e,t,n):(A(H,H.current&1),e=tt(e,t,n),e!==null?e.sibling:null);A(H,H.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ac(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),A(H,H.current),r)break;return null;case 22:case 23:return t.lanes=0,oc(e,t,n)}return tt(e,t,n)}var cc,bo,dc,fc;cc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};bo=function(){};dc=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,At(Qe.current);var o=null;switch(n){case"input":l=ko(e,l),r=ko(e,r),o=[];break;case"select":l=G({},l,{value:void 0}),r=G({},r,{value:void 0}),o=[];break;case"textarea":l=No(e,l),r=No(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ul)}jo(n,r);var i;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var s=l[c];for(i in s)s.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Jn.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(s=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(u!=null||s!=null))if(c==="style")if(s){for(i in s)!s.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in u)u.hasOwnProperty(i)&&s[i]!==u[i]&&(n||(n={}),n[i]=u[i])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,s=s?s.__html:void 0,u!=null&&s!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Jn.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&V("scroll",e),o||s===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};fc=function(e,t,n,r){n!==r&&(t.flags|=4)};function In(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ie(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Kf(e,t,n){var r=t.pendingProps;switch(Li(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ie(t),null;case 1:return ve(t.type)&&al(),ie(t),null;case 3:return r=t.stateNode,kn(),B(ge),B(ue),$i(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Mr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Fe!==null&&(si(Fe),Fe=null))),bo(e,t),ie(t),null;case 5:Ui(t);var l=At(ar.current);if(n=t.type,e!==null&&t.stateNode!=null)dc(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(y(166));return ie(t),null}if(e=At(Qe.current),Mr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[We]=t,r[sr]=o,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(l=0;l<Vn.length;l++)V(Vn[l],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":ds(r,o),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},V("invalid",r);break;case"textarea":ps(r,o),V("invalid",r)}jo(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var s=o[i];i==="children"?typeof s=="string"?r.textContent!==s&&(o.suppressHydrationWarning!==!0&&Rr(r.textContent,s,e),l=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(o.suppressHydrationWarning!==!0&&Rr(r.textContent,s,e),l=["children",""+s]):Jn.hasOwnProperty(i)&&s!=null&&i==="onScroll"&&V("scroll",r)}switch(n){case"input":Nr(r),fs(r,o,!0);break;case"textarea":Nr(r),ms(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=ul)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=$u(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[We]=t,e[sr]=r,cc(e,t,!1,!1),t.stateNode=e;e:{switch(i=_o(n,r),n){case"dialog":V("cancel",e),V("close",e),l=r;break;case"iframe":case"object":case"embed":V("load",e),l=r;break;case"video":case"audio":for(l=0;l<Vn.length;l++)V(Vn[l],e);l=r;break;case"source":V("error",e),l=r;break;case"img":case"image":case"link":V("error",e),V("load",e),l=r;break;case"details":V("toggle",e),l=r;break;case"input":ds(e,r),l=ko(e,r),V("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=G({},r,{value:void 0}),V("invalid",e);break;case"textarea":ps(e,r),l=No(e,r),V("invalid",e);break;default:l=r}jo(n,l),s=l;for(o in s)if(s.hasOwnProperty(o)){var u=s[o];o==="style"?Wu(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Vu(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&qn(e,u):typeof u=="number"&&qn(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Jn.hasOwnProperty(o)?u!=null&&o==="onScroll"&&V("scroll",e):u!=null&&gi(e,o,u,i))}switch(n){case"input":Nr(e),fs(e,r,!1);break;case"textarea":Nr(e),ms(e);break;case"option":r.value!=null&&e.setAttribute("value",""+St(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?cn(e,!!r.multiple,o,!1):r.defaultValue!=null&&cn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=ul)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ie(t),null;case 6:if(e&&t.stateNode!=null)fc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(y(166));if(n=At(ar.current),At(Qe.current),Mr(t)){if(r=t.stateNode,n=t.memoizedProps,r[We]=t,(o=r.nodeValue!==n)&&(e=ke,e!==null))switch(e.tag){case 3:Rr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Rr(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[We]=t,t.stateNode=r}return ie(t),null;case 13:if(B(H),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&we!==null&&t.mode&1&&!(t.flags&128))Ta(),xn(),t.flags|=98560,o=!1;else if(o=Mr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(y(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(y(317));o[We]=t}else xn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ie(t),o=!1}else Fe!==null&&(si(Fe),Fe=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||H.current&1?q===0&&(q=3):qi())),t.updateQueue!==null&&(t.flags|=4),ie(t),null);case 4:return kn(),bo(e,t),e===null&&or(t.stateNode.containerInfo),ie(t),null;case 10:return Oi(t.type._context),ie(t),null;case 17:return ve(t.type)&&al(),ie(t),null;case 19:if(B(H),o=t.memoizedState,o===null)return ie(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)In(o,!1);else{if(q!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=gl(e),i!==null){for(t.flags|=128,In(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return A(H,H.current&1|2),t.child}e=e.sibling}o.tail!==null&&Y()>Cn&&(t.flags|=128,r=!0,In(o,!1),t.lanes=4194304)}else{if(!r)if(e=gl(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),In(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!W)return ie(t),null}else 2*Y()-o.renderingStartTime>Cn&&n!==1073741824&&(t.flags|=128,r=!0,In(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Y(),t.sibling=null,n=H.current,A(H,r?n&1|2:n&1),t):(ie(t),null);case 22:case 23:return Ji(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?xe&1073741824&&(ie(t),t.subtreeFlags&6&&(t.flags|=8192)):ie(t),null;case 24:return null;case 25:return null}throw Error(y(156,t.tag))}function Yf(e,t){switch(Li(t),t.tag){case 1:return ve(t.type)&&al(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return kn(),B(ge),B(ue),$i(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ui(t),null;case 13:if(B(H),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(y(340));xn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return B(H),null;case 4:return kn(),null;case 10:return Oi(t.type._context),null;case 22:case 23:return Ji(),null;case 24:return null;default:return null}}var Dr=!1,se=!1,Xf=typeof WeakSet=="function"?WeakSet:Set,N=null;function un(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){K(e,t,r)}else n.current=null}function ei(e,t,n){try{n()}catch(r){K(e,t,r)}}var ru=!1;function Zf(e,t){if(Fo=ol,e=va(),Pi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,s=-1,u=-1,c=0,g=0,h=e,m=null;t:for(;;){for(var w;h!==n||l!==0&&h.nodeType!==3||(s=i+l),h!==o||r!==0&&h.nodeType!==3||(u=i+r),h.nodeType===3&&(i+=h.nodeValue.length),(w=h.firstChild)!==null;)m=h,h=w;for(;;){if(h===e)break t;if(m===n&&++c===l&&(s=i),m===o&&++g===r&&(u=i),(w=h.nextSibling)!==null)break;h=m,m=h.parentNode}h=w}n=s===-1||u===-1?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ao={focusedElem:e,selectionRange:n},ol=!1,N=t;N!==null;)if(t=N,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,N=e;else for(;N!==null;){t=N;try{var k=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var x=k.memoizedProps,P=k.memoizedState,d=t.stateNode,a=d.getSnapshotBeforeUpdate(t.elementType===t.type?x:Oe(t.type,x),P);d.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(y(163))}}catch(v){K(t,t.return,v)}if(e=t.sibling,e!==null){e.return=t.return,N=e;break}N=t.return}return k=ru,ru=!1,k}function Yn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&ei(t,n,o)}l=l.next}while(l!==r)}}function Ml(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ti(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function pc(e){var t=e.alternate;t!==null&&(e.alternate=null,pc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[We],delete t[sr],delete t[Vo],delete t[Rf],delete t[Mf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mc(e){return e.tag===5||e.tag===3||e.tag===4}function lu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ni(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ul));else if(r!==4&&(e=e.child,e!==null))for(ni(e,t,n),e=e.sibling;e!==null;)ni(e,t,n),e=e.sibling}function ri(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ri(e,t,n),e=e.sibling;e!==null;)ri(e,t,n),e=e.sibling}var ne=null,De=!1;function ot(e,t,n){for(n=n.child;n!==null;)hc(e,t,n),n=n.sibling}function hc(e,t,n){if(He&&typeof He.onCommitFiberUnmount=="function")try{He.onCommitFiberUnmount(El,n)}catch{}switch(n.tag){case 5:se||un(n,t);case 6:var r=ne,l=De;ne=null,ot(e,t,n),ne=r,De=l,ne!==null&&(De?(e=ne,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ne.removeChild(n.stateNode));break;case 18:ne!==null&&(De?(e=ne,n=n.stateNode,e.nodeType===8?ro(e.parentNode,n):e.nodeType===1&&ro(e,n),nr(e)):ro(ne,n.stateNode));break;case 4:r=ne,l=De,ne=n.stateNode.containerInfo,De=!0,ot(e,t,n),ne=r,De=l;break;case 0:case 11:case 14:case 15:if(!se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&ei(n,t,i),l=l.next}while(l!==r)}ot(e,t,n);break;case 1:if(!se&&(un(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){K(n,t,s)}ot(e,t,n);break;case 21:ot(e,t,n);break;case 22:n.mode&1?(se=(r=se)||n.memoizedState!==null,ot(e,t,n),se=r):ot(e,t,n);break;default:ot(e,t,n)}}function ou(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Xf),t.forEach(function(r){var l=op.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Me(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,i=t,s=i;e:for(;s!==null;){switch(s.tag){case 5:ne=s.stateNode,De=!1;break e;case 3:ne=s.stateNode.containerInfo,De=!0;break e;case 4:ne=s.stateNode.containerInfo,De=!0;break e}s=s.return}if(ne===null)throw Error(y(160));hc(o,i,l),ne=null,De=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(c){K(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)gc(t,e),t=t.sibling}function gc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Me(t,e),Ve(e),r&4){try{Yn(3,e,e.return),Ml(3,e)}catch(x){K(e,e.return,x)}try{Yn(5,e,e.return)}catch(x){K(e,e.return,x)}}break;case 1:Me(t,e),Ve(e),r&512&&n!==null&&un(n,n.return);break;case 5:if(Me(t,e),Ve(e),r&512&&n!==null&&un(n,n.return),e.flags&32){var l=e.stateNode;try{qn(l,"")}catch(x){K(e,e.return,x)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,s=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{s==="input"&&o.type==="radio"&&o.name!=null&&Au(l,o),_o(s,i);var c=_o(s,o);for(i=0;i<u.length;i+=2){var g=u[i],h=u[i+1];g==="style"?Wu(l,h):g==="dangerouslySetInnerHTML"?Vu(l,h):g==="children"?qn(l,h):gi(l,g,h,c)}switch(s){case"input":So(l,o);break;case"textarea":Uu(l,o);break;case"select":var m=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var w=o.value;w!=null?cn(l,!!o.multiple,w,!1):m!==!!o.multiple&&(o.defaultValue!=null?cn(l,!!o.multiple,o.defaultValue,!0):cn(l,!!o.multiple,o.multiple?[]:"",!1))}l[sr]=o}catch(x){K(e,e.return,x)}}break;case 6:if(Me(t,e),Ve(e),r&4){if(e.stateNode===null)throw Error(y(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(x){K(e,e.return,x)}}break;case 3:if(Me(t,e),Ve(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{nr(t.containerInfo)}catch(x){K(e,e.return,x)}break;case 4:Me(t,e),Ve(e);break;case 13:Me(t,e),Ve(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Xi=Y())),r&4&&ou(e);break;case 22:if(g=n!==null&&n.memoizedState!==null,e.mode&1?(se=(c=se)||g,Me(t,e),se=c):Me(t,e),Ve(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!g&&e.mode&1)for(N=e,g=e.child;g!==null;){for(h=N=g;N!==null;){switch(m=N,w=m.child,m.tag){case 0:case 11:case 14:case 15:Yn(4,m,m.return);break;case 1:un(m,m.return);var k=m.stateNode;if(typeof k.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(x){K(r,n,x)}}break;case 5:un(m,m.return);break;case 22:if(m.memoizedState!==null){su(h);continue}}w!==null?(w.return=m,N=w):su(h)}g=g.sibling}e:for(g=null,h=e;;){if(h.tag===5){if(g===null){g=h;try{l=h.stateNode,c?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(s=h.stateNode,u=h.memoizedProps.style,i=u!=null&&u.hasOwnProperty("display")?u.display:null,s.style.display=Bu("display",i))}catch(x){K(e,e.return,x)}}}else if(h.tag===6){if(g===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(x){K(e,e.return,x)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;g===h&&(g=null),h=h.return}g===h&&(g=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Me(t,e),Ve(e),r&4&&ou(e);break;case 21:break;default:Me(t,e),Ve(e)}}function Ve(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mc(n)){var r=n;break e}n=n.return}throw Error(y(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(qn(l,""),r.flags&=-33);var o=lu(e);ri(e,o,l);break;case 3:case 4:var i=r.stateNode.containerInfo,s=lu(e);ni(e,s,i);break;default:throw Error(y(161))}}catch(u){K(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Jf(e,t,n){N=e,vc(e)}function vc(e,t,n){for(var r=(e.mode&1)!==0;N!==null;){var l=N,o=l.child;if(l.tag===22&&r){var i=l.memoizedState!==null||Dr;if(!i){var s=l.alternate,u=s!==null&&s.memoizedState!==null||se;s=Dr;var c=se;if(Dr=i,(se=u)&&!c)for(N=l;N!==null;)i=N,u=i.child,i.tag===22&&i.memoizedState!==null?uu(l):u!==null?(u.return=i,N=u):uu(l);for(;o!==null;)N=o,vc(o),o=o.sibling;N=l,Dr=s,se=c}iu(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,N=o):iu(e)}}function iu(e){for(;N!==null;){var t=N;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:se||Ml(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!se)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Oe(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Hs(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Hs(t,i,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var g=c.memoizedState;if(g!==null){var h=g.dehydrated;h!==null&&nr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(y(163))}se||t.flags&512&&ti(t)}catch(m){K(t,t.return,m)}}if(t===e){N=null;break}if(n=t.sibling,n!==null){n.return=t.return,N=n;break}N=t.return}}function su(e){for(;N!==null;){var t=N;if(t===e){N=null;break}var n=t.sibling;if(n!==null){n.return=t.return,N=n;break}N=t.return}}function uu(e){for(;N!==null;){var t=N;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ml(4,t)}catch(u){K(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){K(t,l,u)}}var o=t.return;try{ti(t)}catch(u){K(t,o,u)}break;case 5:var i=t.return;try{ti(t)}catch(u){K(t,i,u)}}}catch(u){K(t,t.return,u)}if(t===e){N=null;break}var s=t.sibling;if(s!==null){s.return=t.return,N=s;break}N=t.return}}var qf=Math.ceil,xl=nt.ReactCurrentDispatcher,Ki=nt.ReactCurrentOwner,Pe=nt.ReactCurrentBatchConfig,I=0,te=null,Z=null,re=0,xe=0,an=Et(0),q=0,pr=null,Ht=0,Il=0,Yi=0,Xn=null,me=null,Xi=0,Cn=1/0,Ke=null,wl=!1,li=null,xt=null,Fr=!1,pt=null,kl=0,Zn=0,oi=null,Xr=-1,Zr=0;function de(){return I&6?Y():Xr!==-1?Xr:Xr=Y()}function wt(e){return e.mode&1?I&2&&re!==0?re&-re:Of.transition!==null?(Zr===0&&(Zr=ta()),Zr):(e=F,e!==0||(e=window.event,e=e===void 0?16:ua(e.type)),e):1}function Ue(e,t,n,r){if(50<Zn)throw Zn=0,oi=null,Error(y(185));hr(e,n,r),(!(I&2)||e!==te)&&(e===te&&(!(I&2)&&(Il|=n),q===4&&dt(e,re)),ye(e,r),n===1&&I===0&&!(t.mode&1)&&(Cn=Y()+500,Tl&&jt()))}function ye(e,t){var n=e.callbackNode;Id(e,t);var r=ll(e,e===te?re:0);if(r===0)n!==null&&vs(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&vs(n),t===1)e.tag===0?If(au.bind(null,e)):_a(au.bind(null,e)),Tf(function(){!(I&6)&&jt()}),n=null;else{switch(na(r)){case 1:n=ki;break;case 4:n=bu;break;case 16:n=rl;break;case 536870912:n=ea;break;default:n=rl}n=Ec(n,yc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function yc(e,t){if(Xr=-1,Zr=0,I&6)throw Error(y(327));var n=e.callbackNode;if(hn()&&e.callbackNode!==n)return null;var r=ll(e,e===te?re:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Sl(e,r);else{t=r;var l=I;I|=2;var o=wc();(te!==e||re!==t)&&(Ke=null,Cn=Y()+500,Ut(e,t));do try{tp();break}catch(s){xc(e,s)}while(1);Ii(),xl.current=o,I=l,Z!==null?t=0:(te=null,re=0,t=q)}if(t!==0){if(t===2&&(l=Ro(e),l!==0&&(r=l,t=ii(e,l))),t===1)throw n=pr,Ut(e,0),dt(e,r),ye(e,Y()),n;if(t===6)dt(e,r);else{if(l=e.current.alternate,!(r&30)&&!bf(l)&&(t=Sl(e,r),t===2&&(o=Ro(e),o!==0&&(r=o,t=ii(e,o))),t===1))throw n=pr,Ut(e,0),dt(e,r),ye(e,Y()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(y(345));case 2:Lt(e,me,Ke);break;case 3:if(dt(e,r),(r&130023424)===r&&(t=Xi+500-Y(),10<t)){if(ll(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){de(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=$o(Lt.bind(null,e,me,Ke),t);break}Lt(e,me,Ke);break;case 4:if(dt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var i=31-Ae(r);o=1<<i,i=t[i],i>l&&(l=i),r&=~o}if(r=l,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*qf(r/1960))-r,10<r){e.timeoutHandle=$o(Lt.bind(null,e,me,Ke),r);break}Lt(e,me,Ke);break;case 5:Lt(e,me,Ke);break;default:throw Error(y(329))}}}return ye(e,Y()),e.callbackNode===n?yc.bind(null,e):null}function ii(e,t){var n=Xn;return e.current.memoizedState.isDehydrated&&(Ut(e,t).flags|=256),e=Sl(e,t),e!==2&&(t=me,me=n,t!==null&&si(t)),e}function si(e){me===null?me=e:me.push.apply(me,e)}function bf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!$e(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function dt(e,t){for(t&=~Yi,t&=~Il,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ae(t),r=1<<n;e[n]=-1,t&=~r}}function au(e){if(I&6)throw Error(y(327));hn();var t=ll(e,0);if(!(t&1))return ye(e,Y()),null;var n=Sl(e,t);if(e.tag!==0&&n===2){var r=Ro(e);r!==0&&(t=r,n=ii(e,r))}if(n===1)throw n=pr,Ut(e,0),dt(e,t),ye(e,Y()),n;if(n===6)throw Error(y(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Lt(e,me,Ke),ye(e,Y()),null}function Zi(e,t){var n=I;I|=1;try{return e(t)}finally{I=n,I===0&&(Cn=Y()+500,Tl&&jt())}}function Qt(e){pt!==null&&pt.tag===0&&!(I&6)&&hn();var t=I;I|=1;var n=Pe.transition,r=F;try{if(Pe.transition=null,F=1,e)return e()}finally{F=r,Pe.transition=n,I=t,!(I&6)&&jt()}}function Ji(){xe=an.current,B(an)}function Ut(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Pf(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(Li(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&al();break;case 3:kn(),B(ge),B(ue),$i();break;case 5:Ui(r);break;case 4:kn();break;case 13:B(H);break;case 19:B(H);break;case 10:Oi(r.type._context);break;case 22:case 23:Ji()}n=n.return}if(te=e,Z=e=kt(e.current,null),re=xe=t,q=0,pr=null,Yi=Il=Ht=0,me=Xn=null,Ft!==null){for(t=0;t<Ft.length;t++)if(n=Ft[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,r.next=i}n.pending=r}Ft=null}return e}function xc(e,t){do{var n=Z;try{if(Ii(),Gr.current=yl,vl){for(var r=Q.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}vl=!1}if(Wt=0,ee=J=Q=null,Kn=!1,cr=0,Ki.current=null,n===null||n.return===null){q=1,pr=t,Z=null;break}e:{var o=e,i=n.return,s=n,u=t;if(t=re,s.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,g=s,h=g.tag;if(!(g.mode&1)&&(h===0||h===11||h===15)){var m=g.alternate;m?(g.updateQueue=m.updateQueue,g.memoizedState=m.memoizedState,g.lanes=m.lanes):(g.updateQueue=null,g.memoizedState=null)}var w=Zs(i);if(w!==null){w.flags&=-257,Js(w,i,s,o,t),w.mode&1&&Xs(o,c,t),t=w,u=c;var k=t.updateQueue;if(k===null){var x=new Set;x.add(u),t.updateQueue=x}else k.add(u);break e}else{if(!(t&1)){Xs(o,c,t),qi();break e}u=Error(y(426))}}else if(W&&s.mode&1){var P=Zs(i);if(P!==null){!(P.flags&65536)&&(P.flags|=256),Js(P,i,s,o,t),Ri(Sn(u,s));break e}}o=u=Sn(u,s),q!==4&&(q=2),Xn===null?Xn=[o]:Xn.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var d=nc(o,u,t);Ws(o,d);break e;case 1:s=u;var a=o.type,f=o.stateNode;if(!(o.flags&128)&&(typeof a.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(xt===null||!xt.has(f)))){o.flags|=65536,t&=-t,o.lanes|=t;var v=rc(o,s,t);Ws(o,v);break e}}o=o.return}while(o!==null)}Sc(n)}catch(S){t=S,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(1)}function wc(){var e=xl.current;return xl.current=yl,e===null?yl:e}function qi(){(q===0||q===3||q===2)&&(q=4),te===null||!(Ht&268435455)&&!(Il&268435455)||dt(te,re)}function Sl(e,t){var n=I;I|=2;var r=wc();(te!==e||re!==t)&&(Ke=null,Ut(e,t));do try{ep();break}catch(l){xc(e,l)}while(1);if(Ii(),I=n,xl.current=r,Z!==null)throw Error(y(261));return te=null,re=0,q}function ep(){for(;Z!==null;)kc(Z)}function tp(){for(;Z!==null&&!Ed();)kc(Z)}function kc(e){var t=Nc(e.alternate,e,xe);e.memoizedProps=e.pendingProps,t===null?Sc(e):Z=t,Ki.current=null}function Sc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Yf(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{q=6,Z=null;return}}else if(n=Kf(n,t,xe),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);q===0&&(q=5)}function Lt(e,t,n){var r=F,l=Pe.transition;try{Pe.transition=null,F=1,np(e,t,n,r)}finally{Pe.transition=l,F=r}return null}function np(e,t,n,r){do hn();while(pt!==null);if(I&6)throw Error(y(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(y(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Od(e,o),e===te&&(Z=te=null,re=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Fr||(Fr=!0,Ec(rl,function(){return hn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Pe.transition,Pe.transition=null;var i=F;F=1;var s=I;I|=4,Ki.current=null,Zf(e,n),gc(n,e),Sf(Ao),ol=!!Fo,Ao=Fo=null,e.current=n,Jf(n),jd(),I=s,F=i,Pe.transition=o}else e.current=n;if(Fr&&(Fr=!1,pt=e,kl=l),o=e.pendingLanes,o===0&&(xt=null),Pd(n.stateNode),ye(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(wl)throw wl=!1,e=li,li=null,e;return kl&1&&e.tag!==0&&hn(),o=e.pendingLanes,o&1?e===oi?Zn++:(Zn=0,oi=e):Zn=0,jt(),null}function hn(){if(pt!==null){var e=na(kl),t=Pe.transition,n=F;try{if(Pe.transition=null,F=16>e?16:e,pt===null)var r=!1;else{if(e=pt,pt=null,kl=0,I&6)throw Error(y(331));var l=I;for(I|=4,N=e.current;N!==null;){var o=N,i=o.child;if(N.flags&16){var s=o.deletions;if(s!==null){for(var u=0;u<s.length;u++){var c=s[u];for(N=c;N!==null;){var g=N;switch(g.tag){case 0:case 11:case 15:Yn(8,g,o)}var h=g.child;if(h!==null)h.return=g,N=h;else for(;N!==null;){g=N;var m=g.sibling,w=g.return;if(pc(g),g===c){N=null;break}if(m!==null){m.return=w,N=m;break}N=w}}}var k=o.alternate;if(k!==null){var x=k.child;if(x!==null){k.child=null;do{var P=x.sibling;x.sibling=null,x=P}while(x!==null)}}N=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,N=i;else e:for(;N!==null;){if(o=N,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Yn(9,o,o.return)}var d=o.sibling;if(d!==null){d.return=o.return,N=d;break e}N=o.return}}var a=e.current;for(N=a;N!==null;){i=N;var f=i.child;if(i.subtreeFlags&2064&&f!==null)f.return=i,N=f;else e:for(i=a;N!==null;){if(s=N,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Ml(9,s)}}catch(S){K(s,s.return,S)}if(s===i){N=null;break e}var v=s.sibling;if(v!==null){v.return=s.return,N=v;break e}N=s.return}}if(I=l,jt(),He&&typeof He.onPostCommitFiberRoot=="function")try{He.onPostCommitFiberRoot(El,e)}catch{}r=!0}return r}finally{F=n,Pe.transition=t}}return!1}function cu(e,t,n){t=Sn(n,t),t=nc(e,t,1),e=yt(e,t,1),t=de(),e!==null&&(hr(e,1,t),ye(e,t))}function K(e,t,n){if(e.tag===3)cu(e,e,n);else for(;t!==null;){if(t.tag===3){cu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(xt===null||!xt.has(r))){e=Sn(n,e),e=rc(t,e,1),t=yt(t,e,1),e=de(),t!==null&&(hr(t,1,e),ye(t,e));break}}t=t.return}}function rp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=de(),e.pingedLanes|=e.suspendedLanes&n,te===e&&(re&n)===n&&(q===4||q===3&&(re&130023424)===re&&500>Y()-Xi?Ut(e,0):Yi|=n),ye(e,t)}function Cc(e,t){t===0&&(e.mode&1?(t=_r,_r<<=1,!(_r&130023424)&&(_r=4194304)):t=1);var n=de();e=et(e,t),e!==null&&(hr(e,t,n),ye(e,n))}function lp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Cc(e,n)}function op(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(y(314))}r!==null&&r.delete(t),Cc(e,n)}var Nc;Nc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ge.current)he=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return he=!1,Gf(e,t,n);he=!!(e.flags&131072)}else he=!1,W&&t.flags&1048576&&za(t,fl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Yr(e,t),e=t.pendingProps;var l=yn(t,ue.current);mn(t,n),l=Bi(null,t,r,e,l,n);var o=Wi();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ve(r)?(o=!0,cl(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Fi(t),l.updater=Rl,t.stateNode=l,l._reactInternals=t,Ko(t,r,e,n),t=Zo(null,t,r,!0,o,n)):(t.tag=0,W&&o&&Ti(t),ce(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Yr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=sp(r),e=Oe(r,e),l){case 0:t=Xo(null,t,r,e,n);break e;case 1:t=eu(null,t,r,e,n);break e;case 11:t=qs(null,t,r,e,n);break e;case 14:t=bs(null,t,r,Oe(r.type,e),n);break e}throw Error(y(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),Xo(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),eu(e,t,r,l,n);case 3:e:{if(sc(t),e===null)throw Error(y(387));r=t.pendingProps,o=t.memoizedState,l=o.element,Ia(e,t),hl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=Sn(Error(y(423)),t),t=tu(e,t,r,n,l);break e}else if(r!==l){l=Sn(Error(y(424)),t),t=tu(e,t,r,n,l);break e}else for(we=vt(t.stateNode.containerInfo.firstChild),ke=t,W=!0,Fe=null,n=Ra(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(xn(),r===l){t=tt(e,t,n);break e}ce(e,t,r,n)}t=t.child}return t;case 5:return Oa(t),e===null&&Ho(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,Uo(r,l)?i=null:o!==null&&Uo(r,o)&&(t.flags|=32),ic(e,t),ce(e,t,i,n),t.child;case 6:return e===null&&Ho(t),null;case 13:return uc(e,t,n);case 4:return Ai(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=wn(t,null,r,n):ce(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),qs(e,t,r,l,n);case 7:return ce(e,t,t.pendingProps,n),t.child;case 8:return ce(e,t,t.pendingProps.children,n),t.child;case 12:return ce(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,A(pl,r._currentValue),r._currentValue=i,o!==null)if($e(o.value,i)){if(o.children===l.children&&!ge.current){t=tt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var s=o.dependencies;if(s!==null){i=o.child;for(var u=s.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=Je(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var g=c.pending;g===null?u.next=u:(u.next=g.next,g.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),Qo(o.return,n,t),s.lanes|=n;break}u=u.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(y(341));i.lanes|=n,s=i.alternate,s!==null&&(s.lanes|=n),Qo(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}ce(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,mn(t,n),l=Te(l),r=r(l),t.flags|=1,ce(e,t,r,n),t.child;case 14:return r=t.type,l=Oe(r,t.pendingProps),l=Oe(r.type,l),bs(e,t,r,l,n);case 15:return lc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),Yr(e,t),t.tag=1,ve(r)?(e=!0,cl(t)):e=!1,mn(t,n),tc(t,r,l),Ko(t,r,l,n),Zo(null,t,r,!0,e,n);case 19:return ac(e,t,n);case 22:return oc(e,t,n)}throw Error(y(156,t.tag))};function Ec(e,t){return qu(e,t)}function ip(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new ip(e,t,n,r)}function bi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sp(e){if(typeof e=="function")return bi(e)?1:0;if(e!=null){if(e=e.$$typeof,e===yi)return 11;if(e===xi)return 14}return 2}function kt(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Jr(e,t,n,r,l,o){var i=2;if(r=e,typeof e=="function")bi(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case qt:return $t(n.children,l,o,t);case vi:i=8,l|=8;break;case vo:return e=ze(12,n,t,l|2),e.elementType=vo,e.lanes=o,e;case yo:return e=ze(13,n,t,l),e.elementType=yo,e.lanes=o,e;case xo:return e=ze(19,n,t,l),e.elementType=xo,e.lanes=o,e;case Ou:return Ol(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Mu:i=10;break e;case Iu:i=9;break e;case yi:i=11;break e;case xi:i=14;break e;case ut:i=16,r=null;break e}throw Error(y(130,e==null?e:typeof e,""))}return t=ze(i,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function $t(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Ol(e,t,n,r){return e=ze(22,e,r,t),e.elementType=Ou,e.lanes=n,e.stateNode={isHidden:!1},e}function fo(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function po(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function up(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Gl(0),this.expirationTimes=Gl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Gl(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function es(e,t,n,r,l,o,i,s,u){return e=new up(e,t,n,s,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ze(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fi(o),e}function ap(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Jt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function jc(e){if(!e)return Ct;e=e._reactInternals;e:{if(Kt(e)!==e||e.tag!==1)throw Error(y(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ve(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(y(171))}if(e.tag===1){var n=e.type;if(ve(n))return ja(e,n,t)}return t}function _c(e,t,n,r,l,o,i,s,u){return e=es(n,r,!0,e,l,o,i,s,u),e.context=jc(null),n=e.current,r=de(),l=wt(n),o=Je(r,l),o.callback=t??null,yt(n,o,l),e.current.lanes=l,hr(e,l,r),ye(e,r),e}function Dl(e,t,n,r){var l=t.current,o=de(),i=wt(l);return n=jc(n),t.context===null?t.context=n:t.pendingContext=n,t=Je(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=yt(l,t,i),e!==null&&(Ue(e,l,i,o),Qr(e,l,i)),i}function Cl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function du(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ts(e,t){du(e,t),(e=e.alternate)&&du(e,t)}function cp(){return null}var zc=typeof reportError=="function"?reportError:function(e){console.error(e)};function ns(e){this._internalRoot=e}Fl.prototype.render=ns.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(y(409));Dl(e,t,null,null)};Fl.prototype.unmount=ns.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Qt(function(){Dl(null,e,null,null)}),t[be]=null}};function Fl(e){this._internalRoot=e}Fl.prototype.unstable_scheduleHydration=function(e){if(e){var t=oa();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ct.length&&t!==0&&t<ct[n].priority;n++);ct.splice(n,0,e),n===0&&sa(e)}};function rs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Al(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function fu(){}function dp(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var c=Cl(i);o.call(c)}}var i=_c(t,r,e,0,null,!1,!1,"",fu);return e._reactRootContainer=i,e[be]=i.current,or(e.nodeType===8?e.parentNode:e),Qt(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var s=r;r=function(){var c=Cl(u);s.call(c)}}var u=es(e,0,!1,null,null,!1,!1,"",fu);return e._reactRootContainer=u,e[be]=u.current,or(e.nodeType===8?e.parentNode:e),Qt(function(){Dl(t,u,n,r)}),u}function Ul(e,t,n,r,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var s=l;l=function(){var u=Cl(i);s.call(u)}}Dl(t,i,e,l)}else i=dp(n,t,e,l,r);return Cl(i)}ra=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=$n(t.pendingLanes);n!==0&&(Si(t,n|1),ye(t,Y()),!(I&6)&&(Cn=Y()+500,jt()))}break;case 13:Qt(function(){var r=et(e,1);if(r!==null){var l=de();Ue(r,e,1,l)}}),ts(e,1)}};Ci=function(e){if(e.tag===13){var t=et(e,134217728);if(t!==null){var n=de();Ue(t,e,134217728,n)}ts(e,134217728)}};la=function(e){if(e.tag===13){var t=wt(e),n=et(e,t);if(n!==null){var r=de();Ue(n,e,t,r)}ts(e,t)}};oa=function(){return F};ia=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Po=function(e,t,n){switch(t){case"input":if(So(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Pl(r);if(!l)throw Error(y(90));Fu(r),So(r,l)}}}break;case"textarea":Uu(e,n);break;case"select":t=n.value,t!=null&&cn(e,!!n.multiple,t,!1)}};Gu=Zi;Ku=Qt;var fp={usingClientEntryPoint:!1,Events:[vr,nn,Pl,Hu,Qu,Zi]},On={findFiberByHostInstance:Dt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},pp={bundleType:On.bundleType,version:On.version,rendererPackageName:On.rendererPackageName,rendererConfig:On.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:nt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Zu(e),e===null?null:e.stateNode},findFiberByHostInstance:On.findFiberByHostInstance||cp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ar=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ar.isDisabled&&Ar.supportsFiber)try{El=Ar.inject(pp),He=Ar}catch{}}Ce.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fp;Ce.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!rs(t))throw Error(y(200));return ap(e,t,null,n)};Ce.createRoot=function(e,t){if(!rs(e))throw Error(y(299));var n=!1,r="",l=zc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=es(e,1,!1,null,null,n,!1,r,l),e[be]=t.current,or(e.nodeType===8?e.parentNode:e),new ns(t)};Ce.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(y(188)):(e=Object.keys(e).join(","),Error(y(268,e)));return e=Zu(t),e=e===null?null:e.stateNode,e};Ce.flushSync=function(e){return Qt(e)};Ce.hydrate=function(e,t,n){if(!Al(t))throw Error(y(200));return Ul(null,e,t,!0,n)};Ce.hydrateRoot=function(e,t,n){if(!rs(e))throw Error(y(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",i=zc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=_c(t,null,e,1,n??null,l,!1,o,i),e[be]=t.current,or(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Fl(t)};Ce.render=function(e,t,n){if(!Al(t))throw Error(y(200));return Ul(null,e,t,!1,n)};Ce.unmountComponentAtNode=function(e){if(!Al(e))throw Error(y(40));return e._reactRootContainer?(Qt(function(){Ul(null,null,e,!1,function(){e._reactRootContainer=null,e[be]=null})}),!0):!1};Ce.unstable_batchedUpdates=Zi;Ce.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Al(n))throw Error(y(200));if(e==null||e._reactInternals===void 0)throw Error(y(38));return Ul(e,t,n,!1,r)};Ce.version="18.3.1-next-f1338f8080-20240426";function Pc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Pc)}catch(e){console.error(e)}}Pc(),Pu.exports=Ce;var mp=Pu.exports,pu=mp;ho.createRoot=pu.createRoot,ho.hydrateRoot=pu.hydrateRoot;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var hp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gp=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),Re=(e,t)=>{const n=X.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:s="",children:u,...c},g)=>X.createElement("svg",{ref:g,...hp,width:l,height:l,stroke:r,strokeWidth:i?Number(o)*24/Number(l):o,className:["lucide",`lucide-${gp(e)}`,s].join(" "),...c},[...t.map(([h,m])=>X.createElement(h,m)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vp=Re("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yp=Re("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xp=Re("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wp=Re("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ur=Re("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kp=Re("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sp=Re("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=Re("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hu=Re("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cp=Re("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Np=Re("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ep=Re("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);function Tc(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(t=0;t<l;t++)e[t]&&(n=Tc(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function jp(){for(var e,t,n=0,r="",l=arguments.length;n<l;n++)(e=arguments[n])&&(t=Tc(e))&&(r&&(r+=" "),r+=t);return r}const ls="-",_p=e=>{const t=Pp(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const s=i.split(ls);return s[0]===""&&s.length!==1&&s.shift(),Lc(s,t)||zp(i)},getConflictingClassGroupIds:(i,s)=>{const u=n[i]||[];return s&&r[i]?[...u,...r[i]]:u}}},Lc=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),l=r?Lc(e.slice(1),r):void 0;if(l)return l;if(t.validators.length===0)return;const o=e.join(ls);return(i=t.validators.find(({validator:s})=>s(o)))==null?void 0:i.classGroupId},gu=/^\[(.+)\]$/,zp=e=>{if(gu.test(e)){const t=gu.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Pp=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Lp(Object.entries(e.classGroups),n).forEach(([o,i])=>{ui(i,r,o,t)}),r},ui=(e,t,n,r)=>{e.forEach(l=>{if(typeof l=="string"){const o=l===""?t:vu(t,l);o.classGroupId=n;return}if(typeof l=="function"){if(Tp(l)){ui(l(r),t,n,r);return}t.validators.push({validator:l,classGroupId:n});return}Object.entries(l).forEach(([o,i])=>{ui(i,vu(t,o),n,r)})})},vu=(e,t)=>{let n=e;return t.split(ls).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Tp=e=>e.isThemeGetter,Lp=(e,t)=>t?e.map(([n,r])=>{const l=r.map(o=>typeof o=="string"?t+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,s])=>[t+i,s])):o);return[n,l]}):e,Rp=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const l=(o,i)=>{n.set(o,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(o){let i=n.get(o);if(i!==void 0)return i;if((i=r.get(o))!==void 0)return l(o,i),i},set(o,i){n.has(o)?n.set(o,i):l(o,i)}}},Rc="!",Mp=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,l=t[0],o=t.length,i=s=>{const u=[];let c=0,g=0,h;for(let P=0;P<s.length;P++){let d=s[P];if(c===0){if(d===l&&(r||s.slice(P,P+o)===t)){u.push(s.slice(g,P)),g=P+o;continue}if(d==="/"){h=P;continue}}d==="["?c++:d==="]"&&c--}const m=u.length===0?s:s.substring(g),w=m.startsWith(Rc),k=w?m.substring(1):m,x=h&&h>g?h-g:void 0;return{modifiers:u,hasImportantModifier:w,baseClassName:k,maybePostfixModifierPosition:x}};return n?s=>n({className:s,parseClassName:i}):i},Ip=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Op=e=>({cache:Rp(e.cacheSize),parseClassName:Mp(e),..._p(e)}),Dp=/\s+/,Fp=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:l}=t,o=[],i=e.trim().split(Dp);let s="";for(let u=i.length-1;u>=0;u-=1){const c=i[u],{modifiers:g,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:w}=n(c);let k=!!w,x=r(k?m.substring(0,w):m);if(!x){if(!k){s=c+(s.length>0?" "+s:s);continue}if(x=r(m),!x){s=c+(s.length>0?" "+s:s);continue}k=!1}const P=Ip(g).join(":"),d=h?P+Rc:P,a=d+x;if(o.includes(a))continue;o.push(a);const f=l(x,k);for(let v=0;v<f.length;++v){const S=f[v];o.push(d+S)}s=c+(s.length>0?" "+s:s)}return s};function Ap(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Mc(t))&&(r&&(r+=" "),r+=n);return r}const Mc=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Mc(e[r]))&&(n&&(n+=" "),n+=t);return n};function Up(e,...t){let n,r,l,o=i;function i(u){const c=t.reduce((g,h)=>h(g),e());return n=Op(c),r=n.cache.get,l=n.cache.set,o=s,s(u)}function s(u){const c=r(u);if(c)return c;const g=Fp(u,n);return l(u,g),g}return function(){return o(Ap.apply(null,arguments))}}const $=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Ic=/^\[(?:([a-z-]+):)?(.+)\]$/i,$p=/^\d+\/\d+$/,Vp=new Set(["px","full","screen"]),Bp=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Wp=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Hp=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Qp=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Gp=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ge=e=>gn(e)||Vp.has(e)||$p.test(e),it=e=>_n(e,"length",em),gn=e=>!!e&&!Number.isNaN(Number(e)),mo=e=>_n(e,"number",gn),Dn=e=>!!e&&Number.isInteger(Number(e)),Kp=e=>e.endsWith("%")&&gn(e.slice(0,-1)),L=e=>Ic.test(e),st=e=>Bp.test(e),Yp=new Set(["length","size","percentage"]),Xp=e=>_n(e,Yp,Oc),Zp=e=>_n(e,"position",Oc),Jp=new Set(["image","url"]),qp=e=>_n(e,Jp,nm),bp=e=>_n(e,"",tm),Fn=()=>!0,_n=(e,t,n)=>{const r=Ic.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},em=e=>Wp.test(e)&&!Hp.test(e),Oc=()=>!1,tm=e=>Qp.test(e),nm=e=>Gp.test(e),rm=()=>{const e=$("colors"),t=$("spacing"),n=$("blur"),r=$("brightness"),l=$("borderColor"),o=$("borderRadius"),i=$("borderSpacing"),s=$("borderWidth"),u=$("contrast"),c=$("grayscale"),g=$("hueRotate"),h=$("invert"),m=$("gap"),w=$("gradientColorStops"),k=$("gradientColorStopPositions"),x=$("inset"),P=$("margin"),d=$("opacity"),a=$("padding"),f=$("saturate"),v=$("scale"),S=$("sepia"),E=$("skew"),j=$("space"),_=$("translate"),U=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],ae=()=>["auto",L,t],D=()=>[L,t],rt=()=>["",Ge,it],_t=()=>["auto",gn,L],xr=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],lt=()=>["solid","dashed","dotted","double","none"],Yt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],C=()=>["start","end","center","between","around","evenly","stretch"],z=()=>["","0",L],T=()=>["auto","avoid","all","avoid-page","page","left","right","column"],O=()=>[gn,L];return{cacheSize:500,separator:":",theme:{colors:[Fn],spacing:[Ge,it],blur:["none","",st,L],brightness:O(),borderColor:[e],borderRadius:["none","","full",st,L],borderSpacing:D(),borderWidth:rt(),contrast:O(),grayscale:z(),hueRotate:O(),invert:z(),gap:D(),gradientColorStops:[e],gradientColorStopPositions:[Kp,it],inset:ae(),margin:ae(),opacity:O(),padding:D(),saturate:O(),scale:O(),sepia:z(),skew:O(),space:D(),translate:D()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[st]}],"break-after":[{"break-after":T()}],"break-before":[{"break-before":T()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...xr(),L]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:U()}],"overscroll-x":[{"overscroll-x":U()}],"overscroll-y":[{"overscroll-y":U()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[x]}],"inset-x":[{"inset-x":[x]}],"inset-y":[{"inset-y":[x]}],start:[{start:[x]}],end:[{end:[x]}],top:[{top:[x]}],right:[{right:[x]}],bottom:[{bottom:[x]}],left:[{left:[x]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Dn,L]}],basis:[{basis:ae()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:z()}],shrink:[{shrink:z()}],order:[{order:["first","last","none",Dn,L]}],"grid-cols":[{"grid-cols":[Fn]}],"col-start-end":[{col:["auto",{span:["full",Dn,L]},L]}],"col-start":[{"col-start":_t()}],"col-end":[{"col-end":_t()}],"grid-rows":[{"grid-rows":[Fn]}],"row-start-end":[{row:["auto",{span:[Dn,L]},L]}],"row-start":[{"row-start":_t()}],"row-end":[{"row-end":_t()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...C()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...C(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...C(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[a]}],px:[{px:[a]}],py:[{py:[a]}],ps:[{ps:[a]}],pe:[{pe:[a]}],pt:[{pt:[a]}],pr:[{pr:[a]}],pb:[{pb:[a]}],pl:[{pl:[a]}],m:[{m:[P]}],mx:[{mx:[P]}],my:[{my:[P]}],ms:[{ms:[P]}],me:[{me:[P]}],mt:[{mt:[P]}],mr:[{mr:[P]}],mb:[{mb:[P]}],ml:[{ml:[P]}],"space-x":[{"space-x":[j]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[j]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[st]},st]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",st,it]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",mo]}],"font-family":[{font:[Fn]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",gn,mo]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ge,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[d]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[d]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...lt(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ge,it]}],"underline-offset":[{"underline-offset":["auto",Ge,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[d]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...xr(),Zp]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Xp]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},qp]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[k]}],"gradient-via-pos":[{via:[k]}],"gradient-to-pos":[{to:[k]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[d]}],"border-style":[{border:[...lt(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[d]}],"divide-style":[{divide:lt()}],"border-color":[{border:[l]}],"border-color-x":[{"border-x":[l]}],"border-color-y":[{"border-y":[l]}],"border-color-s":[{"border-s":[l]}],"border-color-e":[{"border-e":[l]}],"border-color-t":[{"border-t":[l]}],"border-color-r":[{"border-r":[l]}],"border-color-b":[{"border-b":[l]}],"border-color-l":[{"border-l":[l]}],"divide-color":[{divide:[l]}],"outline-style":[{outline:["",...lt()]}],"outline-offset":[{"outline-offset":[Ge,L]}],"outline-w":[{outline:[Ge,it]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:rt()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[d]}],"ring-offset-w":[{"ring-offset":[Ge,it]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",st,bp]}],"shadow-color":[{shadow:[Fn]}],opacity:[{opacity:[d]}],"mix-blend":[{"mix-blend":[...Yt(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Yt()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",st,L]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[g]}],invert:[{invert:[h]}],saturate:[{saturate:[f]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[g]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[d]}],"backdrop-saturate":[{"backdrop-saturate":[f]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:O()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:O()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[v]}],"scale-x":[{"scale-x":[v]}],"scale-y":[{"scale-y":[v]}],rotate:[{rotate:[Dn,L]}],"translate-x":[{"translate-x":[_]}],"translate-y":[{"translate-y":[_]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ge,it,mo]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},lm=Up(rm);function Ee(...e){return lm(jp(e))}const Ie=X.forwardRef(({className:e,variant:t="default",size:n="default",...r},l)=>{const o="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",i={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},s={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"};return p.jsx("button",{className:Ee(o,i[t],s[n],e),ref:l,...r})});Ie.displayName="Button";const Rt=X.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:Ee("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Rt.displayName="Card";const Mt=X.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:Ee("flex flex-col space-y-1.5 p-6",e),...t}));Mt.displayName="CardHeader";const It=X.forwardRef(({className:e,...t},n)=>p.jsx("h3",{ref:n,className:Ee("text-2xl font-semibold leading-none tracking-tight",e),...t}));It.displayName="CardTitle";const Zt=X.forwardRef(({className:e,...t},n)=>p.jsx("p",{ref:n,className:Ee("text-sm text-muted-foreground",e),...t}));Zt.displayName="CardDescription";const Ot=X.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:Ee("p-6 pt-0",e),...t}));Ot.displayName="CardContent";const om=X.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:Ee("flex items-center p-6 pt-0",e),...t}));om.displayName="CardFooter";const ai=X.forwardRef(({className:e,type:t,...n},r)=>p.jsx("input",{type:t,className:Ee("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));ai.displayName="Input";const Dc=X.forwardRef(({className:e,...t},n)=>p.jsx("textarea",{className:Ee("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Dc.displayName="Textarea";const Fc=X.createContext(void 0),Ac=()=>{const e=X.useContext(Fc);if(!e)throw new Error("Tabs components must be used within a Tabs provider");return e},im=({value:e,onValueChange:t,children:n,className:r})=>p.jsx(Fc.Provider,{value:{value:e,onValueChange:t},children:p.jsx("div",{className:Ee("w-full",r),children:n})}),Uc=X.forwardRef(({className:e,...t},n)=>p.jsx("div",{ref:n,className:Ee("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));Uc.displayName="TabsList";const qr=X.forwardRef(({className:e,value:t,...n},r)=>{const{value:l,onValueChange:o}=Ac(),i=l===t;return p.jsx("button",{ref:r,className:Ee("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",i?"bg-background text-foreground shadow-sm":"hover:bg-background/50",e),onClick:()=>o(t),...n})});qr.displayName="TabsTrigger";const br=X.forwardRef(({className:e,value:t,...n},r)=>{const{value:l}=Ac();return l!==t?null:p.jsx("div",{ref:r,className:Ee("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...n})});br.displayName="TabsContent";const sm=()=>{const[e,t]=X.useState({name:"",email:"",message:""}),n=i=>{const{name:s,value:u}=i.target;t(c=>({...c,[s]:u}))},r=i=>{i.preventDefault(),console.log("Form submitted:",e),alert("Thank you for your message! I'll get back to you soon."),t({name:"",email:"",message:""})},l=[{name:"Frontend Development",icon:p.jsx(yp,{className:"w-6 h-6"}),technologies:["React","TypeScript","Tailwind CSS","Next.js"]},{name:"Backend Development",icon:p.jsx(xp,{className:"w-6 h-6"}),technologies:["Node.js","Python","PostgreSQL","MongoDB"]},{name:"Web Technologies",icon:p.jsx(kp,{className:"w-6 h-6"}),technologies:["HTML5","CSS3","JavaScript","REST APIs"]},{name:"Mobile Development",icon:p.jsx(Np,{className:"w-6 h-6"}),technologies:["React Native","Flutter","iOS","Android"]}],o=[{title:"E-Commerce Platform",description:"A full-stack e-commerce solution with React, Node.js, and PostgreSQL",technologies:["React","Node.js","PostgreSQL","Stripe"],github:"https://github.com",demo:"https://demo.com",image:"/api/placeholder/400/250"},{title:"Task Management App",description:"A collaborative task management application with real-time updates",technologies:["Next.js","Socket.io","MongoDB","Tailwind CSS"],github:"https://github.com",demo:"https://demo.com",image:"/api/placeholder/400/250"},{title:"Weather Dashboard",description:"A responsive weather dashboard with location-based forecasts",technologies:["React","TypeScript","Weather API","Chart.js"],github:"https://github.com",demo:"https://demo.com",image:"/api/placeholder/400/250"}];return p.jsxs("div",{className:"min-h-screen bg-background",children:[p.jsx("nav",{className:"fixed top-0 w-full bg-background/80 backdrop-blur-sm border-b z-50",children:p.jsx("div",{className:"container mx-auto px-4 py-4",children:p.jsxs("div",{className:"flex justify-between items-center",children:[p.jsx("h1",{className:"text-2xl font-bold",children:"Portfolio"}),p.jsxs("div",{className:"hidden md:flex space-x-6",children:[p.jsx("a",{href:"#about",className:"hover:text-primary transition-colors",children:"About"}),p.jsx("a",{href:"#skills",className:"hover:text-primary transition-colors",children:"Skills"}),p.jsx("a",{href:"#projects",className:"hover:text-primary transition-colors",children:"Projects"}),p.jsx("a",{href:"#contact",className:"hover:text-primary transition-colors",children:"Contact"})]})]})})}),p.jsx("section",{className:"pt-20 pb-16 px-4",children:p.jsx("div",{className:"container mx-auto text-center",children:p.jsxs("div",{className:"max-w-3xl mx-auto",children:[p.jsxs("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:["Hi, I'm ",p.jsx("span",{className:"text-primary",children:"John Doe"})]}),p.jsx("p",{className:"text-xl md:text-2xl text-muted-foreground mb-8",children:"Full Stack Developer & UI/UX Enthusiast"}),p.jsx("p",{className:"text-lg text-muted-foreground mb-8 max-w-2xl mx-auto",children:"I create beautiful, functional, and user-centered digital experiences. With expertise in modern web technologies, I bring ideas to life through code."}),p.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[p.jsxs(Ie,{size:"lg",className:"text-lg px-8",children:[p.jsx(hu,{className:"w-5 h-5 mr-2"}),"Get In Touch"]}),p.jsxs(Ie,{variant:"outline",size:"lg",className:"text-lg px-8",children:[p.jsx(Ur,{className:"w-5 h-5 mr-2"}),"View My Work"]})]})]})})}),p.jsx("section",{id:"about",className:"py-16 px-4 bg-muted/50",children:p.jsxs("div",{className:"container mx-auto",children:[p.jsxs("div",{className:"text-center mb-12",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"About Me"}),p.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Passionate developer with 5+ years of experience creating digital solutions"})]}),p.jsxs(im,{value:"experience",onValueChange:()=>{},children:[p.jsxs(Uc,{className:"grid w-full grid-cols-3 max-w-md mx-auto",children:[p.jsxs(qr,{value:"experience",children:[p.jsx(vp,{className:"w-4 h-4 mr-2"}),"Experience"]}),p.jsxs(qr,{value:"education",children:[p.jsx(Sp,{className:"w-4 h-4 mr-2"}),"Education"]}),p.jsxs(qr,{value:"personal",children:[p.jsx(Ep,{className:"w-4 h-4 mr-2"}),"Personal"]})]}),p.jsx(br,{value:"experience",className:"mt-8",children:p.jsxs("div",{className:"grid md:grid-cols-2 gap-6 max-w-4xl mx-auto",children:[p.jsxs(Rt,{children:[p.jsxs(Mt,{children:[p.jsx(It,{children:"Senior Frontend Developer"}),p.jsx(Zt,{children:"TechCorp Inc. • 2022 - Present"})]}),p.jsx(Ot,{children:p.jsx("p",{className:"text-muted-foreground",children:"Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React patterns."})})]}),p.jsxs(Rt,{children:[p.jsxs(Mt,{children:[p.jsx(It,{children:"Full Stack Developer"}),p.jsx(Zt,{children:"StartupXYZ • 2020 - 2022"})]}),p.jsx(Ot,{children:p.jsx("p",{className:"text-muted-foreground",children:"Built scalable web applications from scratch, worked with cross-functional teams, and contributed to product strategy decisions."})})]})]})}),p.jsx(br,{value:"education",className:"mt-8",children:p.jsx("div",{className:"max-w-2xl mx-auto",children:p.jsxs(Rt,{children:[p.jsxs(Mt,{children:[p.jsx(It,{children:"Bachelor of Computer Science"}),p.jsx(Zt,{children:"University of Technology • 2016 - 2020"})]}),p.jsx(Ot,{children:p.jsx("p",{className:"text-muted-foreground",children:"Graduated with honors. Specialized in software engineering and web development. Active in coding clubs and hackathons."})})]})})}),p.jsx(br,{value:"personal",className:"mt-8",children:p.jsxs("div",{className:"max-w-2xl mx-auto text-center",children:[p.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"When I'm not coding, you can find me exploring new technologies, contributing to open source projects, or enjoying outdoor activities. I believe in continuous learning and staying up-to-date with the latest industry trends."}),p.jsxs("div",{className:"flex justify-center space-x-4",children:[p.jsxs(Ie,{variant:"outline",size:"sm",children:[p.jsx(Ur,{className:"w-4 h-4 mr-2"}),"GitHub"]}),p.jsxs(Ie,{variant:"outline",size:"sm",children:[p.jsx(mu,{className:"w-4 h-4 mr-2"}),"LinkedIn"]})]})]})})]})]})}),p.jsx("section",{id:"skills",className:"py-16 px-4",children:p.jsxs("div",{className:"container mx-auto",children:[p.jsxs("div",{className:"text-center mb-12",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Skills & Technologies"}),p.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"A comprehensive toolkit for building modern web applications"})]}),p.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:l.map((i,s)=>p.jsxs(Rt,{className:"text-center hover:shadow-lg transition-shadow",children:[p.jsxs(Mt,{children:[p.jsx("div",{className:"flex justify-center mb-4",children:p.jsx("div",{className:"p-3 bg-primary/10 rounded-full text-primary",children:i.icon})}),p.jsx(It,{className:"text-lg",children:i.name})]}),p.jsx(Ot,{children:p.jsx("div",{className:"flex flex-wrap gap-2 justify-center",children:i.technologies.map((u,c)=>p.jsx("span",{className:"px-2 py-1 bg-muted text-muted-foreground text-sm rounded",children:u},c))})})]},s))})]})}),p.jsx("section",{id:"projects",className:"py-16 px-4 bg-muted/50",children:p.jsxs("div",{className:"container mx-auto",children:[p.jsxs("div",{className:"text-center mb-12",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Featured Projects"}),p.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"A selection of my recent work and personal projects"})]}),p.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:o.map((i,s)=>p.jsxs(Rt,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[p.jsx("div",{className:"aspect-video bg-muted flex items-center justify-center",children:p.jsx("div",{className:"text-muted-foreground",children:"Project Image"})}),p.jsxs(Mt,{children:[p.jsx(It,{children:i.title}),p.jsx(Zt,{children:i.description})]}),p.jsxs(Ot,{children:[p.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:i.technologies.map((u,c)=>p.jsx("span",{className:"px-2 py-1 bg-primary/10 text-primary text-sm rounded",children:u},c))}),p.jsxs("div",{className:"flex gap-2",children:[p.jsxs(Ie,{variant:"outline",size:"sm",className:"flex-1",children:[p.jsx(Ur,{className:"w-4 h-4 mr-2"}),"Code"]}),p.jsxs(Ie,{size:"sm",className:"flex-1",children:[p.jsx(wp,{className:"w-4 h-4 mr-2"}),"Demo"]})]})]})]},s))})]})}),p.jsx("section",{id:"contact",className:"py-16 px-4",children:p.jsxs("div",{className:"container mx-auto",children:[p.jsxs("div",{className:"text-center mb-12",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Get In Touch"}),p.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Have a project in mind? Let's work together to bring your ideas to life."})]}),p.jsx("div",{className:"max-w-2xl mx-auto",children:p.jsxs(Rt,{children:[p.jsxs(Mt,{children:[p.jsx(It,{children:"Send me a message"}),p.jsx(Zt,{children:"I'll get back to you as soon as possible"})]}),p.jsx(Ot,{children:p.jsxs("form",{onSubmit:r,className:"space-y-4",children:[p.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[p.jsxs("div",{children:[p.jsx("label",{htmlFor:"name",className:"block text-sm font-medium mb-2",children:"Name"}),p.jsx(ai,{id:"name",name:"name",value:e.name,onChange:n,placeholder:"Your name",required:!0})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"email",className:"block text-sm font-medium mb-2",children:"Email"}),p.jsx(ai,{id:"email",name:"email",type:"email",value:e.email,onChange:n,placeholder:"<EMAIL>",required:!0})]})]}),p.jsxs("div",{children:[p.jsx("label",{htmlFor:"message",className:"block text-sm font-medium mb-2",children:"Message"}),p.jsx(Dc,{id:"message",name:"message",value:e.message,onChange:n,placeholder:"Tell me about your project...",rows:5,required:!0})]}),p.jsxs(Ie,{type:"submit",className:"w-full",children:[p.jsx(Cp,{className:"w-4 h-4 mr-2"}),"Send Message"]})]})})]})})]})}),p.jsx("footer",{className:"py-8 px-4 border-t bg-muted/50",children:p.jsx("div",{className:"container mx-auto",children:p.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[p.jsx("p",{className:"text-muted-foreground mb-4 md:mb-0",children:"© 2024 John Doe. All rights reserved."}),p.jsxs("div",{className:"flex space-x-4",children:[p.jsx(Ie,{variant:"ghost",size:"sm",children:p.jsx(Ur,{className:"w-4 h-4"})}),p.jsx(Ie,{variant:"ghost",size:"sm",children:p.jsx(mu,{className:"w-4 h-4"})}),p.jsx(Ie,{variant:"ghost",size:"sm",children:p.jsx(hu,{className:"w-4 h-4"})})]})]})})})]})};function um(){return p.jsx(sm,{})}ho.createRoot(document.getElementById("root")).render(p.jsx(nd.StrictMode,{children:p.jsx(um,{})}));
