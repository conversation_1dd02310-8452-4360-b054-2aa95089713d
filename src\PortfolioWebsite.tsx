import React, { useState } from 'react'
import { 
  Github, 
  Linkedin, 
  Mail, 
  ExternalLink, 
  Code, 
  Database, 
  Globe, 
  Smartphone,
  Send,
  User,
  Briefcase,
  GraduationCap
} from 'lucide-react'
import { 
  Button, 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  Input,
  Textarea,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from './components/ui'

const PortfolioWebsite = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })

  const [activeTab, setActiveTab] = useState('experience')

  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault()
    const element = document.getElementById(targetId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    alert('Thank you for your message! I\'ll get back to you soon.')
    setFormData({ name: '', email: '', message: '' })
  }

  const skills = [
    { name: 'Frontend Development', icon: <Code className="w-6 h-6" />, technologies: ['React', 'TypeScript', 'Tailwind CSS', 'Next.js'] },
    { name: 'Backend Development', icon: <Database className="w-6 h-6" />, technologies: ['Node.js', 'Python', 'PostgreSQL', 'MongoDB'] },
    { name: 'Web Technologies', icon: <Globe className="w-6 h-6" />, technologies: ['HTML5', 'CSS3', 'JavaScript', 'REST APIs'] },
    { name: 'Mobile Development', icon: <Smartphone className="w-6 h-6" />, technologies: ['React Native', 'Flutter', 'iOS', 'Android'] }
  ]

  const projects = [
    {
      title: 'E-Commerce Platform',
      description: 'A full-stack e-commerce solution with React, Node.js, and PostgreSQL',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe'],
      github: 'https://github.com',
      demo: 'https://demo.com',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates',
      technologies: ['Next.js', 'Socket.io', 'MongoDB', 'Tailwind CSS'],
      github: 'https://github.com',
      demo: 'https://demo.com',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Weather Dashboard',
      description: 'A responsive weather dashboard with location-based forecasts',
      technologies: ['React', 'TypeScript', 'Weather API', 'Chart.js'],
      github: 'https://github.com',
      demo: 'https://demo.com',
      image: '/api/placeholder/400/250'
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-background/80 backdrop-blur-sm border-b z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">Portfolio</h1>
            <div className="hidden md:flex space-x-6">
              <a href="#about" onClick={(e) => handleNavClick(e, 'about')} className="hover:text-primary transition-colors">About</a>
              <a href="#skills" onClick={(e) => handleNavClick(e, 'skills')} className="hover:text-primary transition-colors">Skills</a>
              <a href="#projects" onClick={(e) => handleNavClick(e, 'projects')} className="hover:text-primary transition-colors">Projects</a>
              <a href="#contact" onClick={(e) => handleNavClick(e, 'contact')} className="hover:text-primary transition-colors">Contact</a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Hi, I'm <span className="text-primary">John Doe</span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8">
              Full Stack Developer & UI/UX Enthusiast
            </p>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              I create beautiful, functional, and user-centered digital experiences. 
              With expertise in modern web technologies, I bring ideas to life through code.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8" onClick={(e) => handleNavClick(e as any, 'contact')}>
                <Mail className="w-5 h-5 mr-2" />
                Get In Touch
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8" onClick={(e) => handleNavClick(e as any, 'projects')}>
                <Github className="w-5 h-5 mr-2" />
                View My Work
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">About Me</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Passionate developer with 5+ years of experience creating digital solutions
            </p>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto">
              <TabsTrigger value="experience">
                <Briefcase className="w-4 h-4 mr-2" />
                Experience
              </TabsTrigger>
              <TabsTrigger value="education">
                <GraduationCap className="w-4 h-4 mr-2" />
                Education
              </TabsTrigger>
              <TabsTrigger value="personal">
                <User className="w-4 h-4 mr-2" />
                Personal
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="experience" className="mt-8">
              <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle>Senior Frontend Developer</CardTitle>
                    <CardDescription>TechCorp Inc. • 2022 - Present</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Leading frontend development for enterprise applications, 
                      mentoring junior developers, and implementing modern React patterns.
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Full Stack Developer</CardTitle>
                    <CardDescription>StartupXYZ • 2020 - 2022</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Built scalable web applications from scratch, worked with cross-functional 
                      teams, and contributed to product strategy decisions.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="education" className="mt-8">
              <div className="max-w-2xl mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle>Bachelor of Computer Science</CardTitle>
                    <CardDescription>University of Technology • 2016 - 2020</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Graduated with honors. Specialized in software engineering and web development. 
                      Active in coding clubs and hackathons.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="personal" className="mt-8">
              <div className="max-w-2xl mx-auto text-center">
                <p className="text-lg text-muted-foreground mb-6">
                  When I'm not coding, you can find me exploring new technologies, 
                  contributing to open source projects, or enjoying outdoor activities. 
                  I believe in continuous learning and staying up-to-date with the latest industry trends.
                </p>
                <div className="flex justify-center space-x-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open('https://github.com', '_blank')}
                  >
                    <Github className="w-4 h-4 mr-2" />
                    GitHub
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open('https://linkedin.com', '_blank')}
                  >
                    <Linkedin className="w-4 h-4 mr-2" />
                    LinkedIn
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Skills & Technologies</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A comprehensive toolkit for building modern web applications
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {skills.map((skill, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-primary/10 rounded-full text-primary">
                      {skill.icon}
                    </div>
                  </div>
                  <CardTitle className="text-lg">{skill.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="tech-scroll-container h-16 flex items-center">
                    <div
                      className="tech-scroll-content"
                      style={{
                        animationDuration: `${12 + index * 2}s`, // Different speeds for each skill card
                        animationDelay: `${index * 0.5}s` // Staggered start times
                      }}
                    >
                      {/* First set of technologies */}
                      {skill.technologies.map((tech, techIndex) => (
                        <span
                          key={`first-${techIndex}`}
                          className="px-3 py-1 bg-muted text-muted-foreground text-sm rounded whitespace-nowrap flex-shrink-0"
                        >
                          {tech}
                        </span>
                      ))}
                      {/* Duplicate set for seamless loop */}
                      {skill.technologies.map((tech, techIndex) => (
                        <span
                          key={`second-${techIndex}`}
                          className="px-3 py-1 bg-muted text-muted-foreground text-sm rounded whitespace-nowrap flex-shrink-0"
                        >
                          {tech}
                        </span>
                      ))}
                      {/* Third set for extra smoothness */}
                      {skill.technologies.map((tech, techIndex) => (
                        <span
                          key={`third-${techIndex}`}
                          className="px-3 py-1 bg-muted text-muted-foreground text-sm rounded whitespace-nowrap flex-shrink-0"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-16 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Projects</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A selection of my recent work and personal projects
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-video bg-muted flex items-center justify-center">
                  <div className="text-muted-foreground">Project Image</div>
                </div>
                <CardHeader>
                  <CardTitle>{project.title}</CardTitle>
                  <CardDescription>{project.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="tech-scroll-container h-12 flex items-center mb-4">
                    <div
                      className="tech-scroll-content"
                      style={{
                        animationDuration: `${10 + index * 1.5}s`, // Different speeds for each project
                        animationDelay: `${index * 0.3}s` // Staggered start times
                      }}
                    >
                      {/* First set of technologies */}
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={`first-${techIndex}`}
                          className="px-3 py-1 bg-primary/10 text-primary text-sm rounded whitespace-nowrap flex-shrink-0"
                        >
                          {tech}
                        </span>
                      ))}
                      {/* Duplicate set for seamless loop */}
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={`second-${techIndex}`}
                          className="px-3 py-1 bg-primary/10 text-primary text-sm rounded whitespace-nowrap flex-shrink-0"
                        >
                          {tech}
                        </span>
                      ))}
                      {/* Third set for extra smoothness */}
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={`third-${techIndex}`}
                          className="px-3 py-1 bg-primary/10 text-primary text-sm rounded whitespace-nowrap flex-shrink-0"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => window.open(project.github, '_blank')}
                    >
                      <Github className="w-4 h-4 mr-2" />
                      Code
                    </Button>
                    <Button
                      size="sm"
                      className="flex-1"
                      onClick={() => window.open(project.demo, '_blank')}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Demo
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Get In Touch</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Have a project in mind? Let's work together to bring your ideas to life.
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Send me a message</CardTitle>
                <CardDescription>
                  I'll get back to you as soon as possible
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium mb-2">
                        Name
                      </label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Your name"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium mb-2">
                        Email
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium mb-2">
                      Message
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Tell me about your project..."
                      rows={5}
                      required
                    />
                  </div>
                  <Button type="submit" className="w-full">
                    <Send className="w-4 h-4 mr-2" />
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 px-4 border-t bg-muted/50">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-muted-foreground mb-4 md:mb-0">
              © 2024 John Doe. All rights reserved.
            </p>
            <div className="flex space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open('https://github.com', '_blank')}
              >
                <Github className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open('https://linkedin.com', '_blank')}
              >
                <Linkedin className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => handleNavClick(e as any, 'contact')}
              >
                <Mail className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default PortfolioWebsite
